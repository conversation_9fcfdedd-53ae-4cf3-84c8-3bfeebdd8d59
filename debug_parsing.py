#!/usr/bin/env python3
"""
Debug script to analyze exact parsing of tree count values
"""

import pandas as pd

def clean_numeric_value_corrected(value):
    """Clean and convert numeric values with CORRECTED European format handling"""
    if pd.isna(value) or value == '' or value == '-':
        return None

    str_val = str(value).strip()
    if str_val == '-' or str_val == '':
        return None

    # CORRECTED: For tree counts like "7.926", "8.528", "10.533" etc.
    # These are thousands separators, not decimals
    # Only comma is decimal separator in this data
    
    if ',' in str_val and '.' in str_val:
        # Format like 1.234,56 - European style with both separators
        # Remove dots (thousands) and replace comma with dot (decimal)
        str_val = str_val.replace('.', '').replace(',', '.')
    elif ',' in str_val:
        # Only comma present - decimal separator in European format
        str_val = str_val.replace(',', '.')
    elif '.' in str_val:
        # IMPORTANT: For tree counts, dots are ALWAYS thousands separators
        # For areas and SPH, we need to be more careful
        parts = str_val.split('.')
        if len(parts) == 2:
            # Check if this looks like a thousands separator
            # Tree counts: typically have 3 digits after dot (7.926 = 7926)
            # Areas/SPH: typically have 2 digits after dot (4.99 = 4.99)
            if len(parts[1]) >= 3 or int(parts[0]) > 100:
                # Likely thousands separator
                str_val = str_val.replace('.', '')
            # Otherwise keep as decimal

    try:
        return float(str_val)
    except ValueError:
        return None

def debug_parsing():
    """Debug specific parsing cases"""
    
    # Load the raw data
    df = pd.read_csv('data _luas_net_deteksi_otomatis.txt', sep='\t', encoding='utf-8')
    
    print("=== DEBUG PARSING ANALYSIS ===")
    print(f"Total raw records: {len(df)}")
    
    # Sample some tree count values for detailed analysis
    sample_values = df['Total Trees'].head(20).tolist()
    print("\nSample raw tree count values:")
    for i, val in enumerate(sample_values):
        parsed = clean_numeric_value_corrected(val)
        print(f"{i+1:2d}. Raw: '{val}' -> Parsed: {parsed:,.0f}" if parsed else f"{i+1:2d}. Raw: '{val}' -> Parsed: None")
    
    # Parse all tree counts
    df['Total Trees Parsed'] = df['Total Trees'].apply(clean_numeric_value_corrected)
    
    # Filter out zero/null values for sum calculation  
    non_zero_trees = df[df['Total Trees Parsed'] > 0]['Total Trees Parsed']
    total_trees = non_zero_trees.sum()
    
    print(f"\nRecords with tree count > 0: {len(non_zero_trees)}")
    print(f"Records with tree count = 0: {len(df[df['Total Trees Parsed'] == 0])}")
    print(f"Records with null tree count: {df['Total Trees Parsed'].isna().sum()}")
    print(f"Total trees calculated: {total_trees:,.0f}")
    print(f"Expected total trees: 1,465,078")
    print(f"Difference: {1465078 - total_trees:,.0f}")
    
    # Identify largest tree counts to check if they're parsed correctly
    print("\nTop 10 largest tree counts:")
    top_trees = df.nlargest(10, 'Total Trees Parsed')[['Sub Division', 'Block', 'Total Trees', 'Total Trees Parsed']]
    for _, row in top_trees.iterrows():
        print(f"  {row['Sub Division']} | {row['Block']} | Raw: '{row['Total Trees']}' | Parsed: {row['Total Trees Parsed']:,.0f}")
    
    # Check for any obvious parsing errors
    print("\nChecking for parsing errors...")
    for idx, row in df.iterrows():
        raw_val = str(row['Total Trees']).strip()
        parsed_val = row['Total Trees Parsed']
        
        # Flag potential issues
        if '.' in raw_val and parsed_val is not None:
            # For values like "10.533", should become 10533
            if ',' not in raw_val:  # Only dots, no commas
                parts = raw_val.split('.')
                if len(parts) == 2 and len(parts[1]) == 3:
                    expected = int(parts[0] + parts[1])
                    if abs(parsed_val - expected) > 0.1:
                        print(f"  PARSING ERROR: {row['Sub Division']} | {row['Block']} | Raw: '{raw_val}' | Parsed: {parsed_val} | Expected: {expected}")
    
    # Manual calculation check
    print("\n=== MANUAL CALCULATION CHECK ===")
    manual_total = 0
    for _, row in df.iterrows():
        raw_val = str(row['Total Trees']).strip()
        if raw_val == '0' or raw_val == '' or pd.isna(row['Total Trees']):
            continue
        
        # Manual parsing for tree counts
        if '.' in raw_val and ',' not in raw_val:
            # Format like "7.926" = 7926
            cleaned = raw_val.replace('.', '')
            try:
                manual_total += int(cleaned)
            except:
                pass
        elif raw_val.isdigit():
            manual_total += int(raw_val)
        else:
            # Other formats
            try:
                cleaned = raw_val.replace('.', '').replace(',', '.')
                manual_total += int(float(cleaned))
            except:
                pass
    
    print(f"Manual calculation total: {manual_total:,.0f}")
    
    return df

if __name__ == "__main__":
    df = debug_parsing() 
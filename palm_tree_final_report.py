#!/usr/bin/env python3
"""
Final Comprehensive Palm Tree Detection Comparison Report
Creates a professional Excel report comparing 4 data sources:
1. Automatic Detection (Otomatis)
2. Agronomist Field Survey
3. Manuring Operations
4. Manual Detection

Header structure matches the user's exact requirements
"""

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import re
from datetime import datetime

def clean_numeric_value(value):
    """Clean and convert numeric values, handling European format"""
    if pd.isna(value) or value == '' or value == '-':
        return None

    str_val = str(value).strip()
    if str_val == '-' or str_val == '':
        return None

    # Handle European format: comma as decimal separator, dot as thousands separator
    if ',' in str_val and '.' in str_val:
        str_val = str_val.replace('.', '').replace(',', '.')
    elif ',' in str_val:
        str_val = str_val.replace(',', '.')
    elif '.' in str_val:
        parts = str_val.split('.')
        if len(parts) == 2 and len(parts[1]) == 3:
            str_val = str_val.replace('.', '')

    try:
        return float(str_val)
    except ValueError:
        return None

def standardize_block_name(block_name):
    """Standardize block names for matching"""
    if pd.isna(block_name):
        return ''
    
    block = str(block_name).strip().upper()
    block = re.sub(r'\s*/\s*', '/', block)
    block = re.sub(r'^P\s*', 'P ', block)
    
    return block

def load_all_data_sources():
    """Load and process all four data sources"""
    print("Loading all data sources...")
    
    # 1. Load automatic detection data
    auto_df = pd.read_csv('data _luas_net_deteksi_otomatis.txt', sep='\t', encoding='utf-8')
    auto_df.columns = auto_df.columns.str.strip()
    
    numeric_cols = ['Total Area (Ha)', 'Total Trees', 'Net Area (Ha)', 'Unplanted Area (Ha)', 'Average SPH']
    for col in numeric_cols:
        auto_df[col] = auto_df[col].apply(clean_numeric_value)
    
    auto_df['Block_Clean'] = auto_df['Block'].apply(standardize_block_name)
    auto_df['Match_Key'] = auto_df['Sub Division'].str.strip() + '|' + auto_df['Block_Clean']
    
    # 2. Load agronomist/manuring data
    agro_df = pd.read_csv('data_agronomist_manuring.txt', sep='\t', encoding='utf-8')
    agro_df.columns = agro_df.columns.str.strip()
    
    numeric_cols = ['Hasil Agronomist', 'Hasil Manuring']
    for col in numeric_cols:
        agro_df[col] = agro_df[col].apply(clean_numeric_value)
    
    agro_df['Block_Clean'] = agro_df['BLOK'].apply(standardize_block_name)
    agro_df['Match_Key'] = agro_df['Sub Divisi'].str.strip() + '|' + agro_df['Block_Clean']
    
    # 3. Load manual detection data
    try:
        manual_df = pd.read_csv('data_manual_detection.txt', sep='\t', encoding='utf-8')
        manual_df.columns = manual_df.columns.str.strip()
        
        manual_df['Hasil Manual'] = manual_df['Hasil Manual'].apply(clean_numeric_value)
        manual_df['Block_Clean'] = manual_df['BLOK'].apply(standardize_block_name)
        manual_df['Match_Key'] = manual_df['Sub Divisi'].str.strip() + '|' + manual_df['Block_Clean']
    except FileNotFoundError:
        print("Manual detection data not found. Creating empty dataframe.")
        manual_df = pd.DataFrame(columns=['Match_Key', 'Hasil Manual'])
    
    print(f"Loaded {len(auto_df)} automatic detection records")
    print(f"Loaded {len(agro_df)} agronomist/manuring records") 
    print(f"Loaded {len(manual_df)} manual detection records")
    
    return auto_df, agro_df, manual_df

def merge_all_data_sources(auto_df, agro_df, manual_df):
    """Merge all data sources into a comprehensive dataset"""
    print("Merging all data sources...")
    
    # Start with automatic detection as master
    merged_df = auto_df.copy()
    
    # Initialize columns for all detection methods
    merged_df['Trees_Agronomist'] = None
    merged_df['Trees_Manuring'] = None
    merged_df['Trees_Manual'] = None
    merged_df['SPH_Agronomist'] = None
    merged_df['SPH_Manuring'] = None
    merged_df['SPH_Manual'] = None
    
    # Merge agronomist and manuring data
    agro_dict = agro_df.set_index('Match_Key').to_dict('index')
    for idx, row in merged_df.iterrows():
        match_key = row['Match_Key']
        if match_key in agro_dict:
            agro_data = agro_dict[match_key]
            
            # Agronomist data
            if agro_data['Hasil Agronomist'] is not None:
                merged_df.at[idx, 'Trees_Agronomist'] = agro_data['Hasil Agronomist']
                if row['Net Area (Ha)'] and row['Net Area (Ha)'] > 0:
                    merged_df.at[idx, 'SPH_Agronomist'] = agro_data['Hasil Agronomist'] / row['Net Area (Ha)']
            
            # Manuring data
            if agro_data['Hasil Manuring'] is not None:
                merged_df.at[idx, 'Trees_Manuring'] = agro_data['Hasil Manuring']
                if row['Net Area (Ha)'] and row['Net Area (Ha)'] > 0:
                    merged_df.at[idx, 'SPH_Manuring'] = agro_data['Hasil Manuring'] / row['Net Area (Ha)']
    
    # Merge manual detection data
    manual_dict = manual_df.set_index('Match_Key').to_dict('index')
    for idx, row in merged_df.iterrows():
        match_key = row['Match_Key']
        if match_key in manual_dict:
            manual_data = manual_dict[match_key]
            
            if manual_data['Hasil Manual'] is not None:
                merged_df.at[idx, 'Trees_Manual'] = manual_data['Hasil Manual']
                if row['Net Area (Ha)'] and row['Net Area (Ha)'] > 0:
                    merged_df.at[idx, 'SPH_Manual'] = manual_data['Hasil Manual'] / row['Net Area (Ha)']
    
    print(f"Merged dataset contains {len(merged_df)} records")
    return merged_df

def calculate_comprehensive_statistics(merged_df):
    """Calculate comprehensive statistics for all data sources"""
    print("Calculating comprehensive statistics...")
    
    # Basic totals
    total_area = merged_df['Total Area (Ha)'].sum()
    total_net_area = merged_df['Net Area (Ha)'].sum()
    total_unplanted = merged_df['Unplanted Area (Ha)'].sum()
    total_trees_auto = merged_df['Total Trees'].sum()
    total_blocks = len(merged_df)
    
    # Calculate average SPH
    avg_sph = total_trees_auto / total_net_area if total_net_area > 0 else 0
    
    # Count data availability for each method
    agronomist_count = merged_df['Trees_Agronomist'].notna().sum()
    manuring_count = merged_df['Trees_Manuring'].notna().sum()
    manual_count = merged_df['Trees_Manual'].notna().sum()
    
    # Calculate totals for each method
    total_trees_agro = merged_df['Trees_Agronomist'].sum()
    total_trees_manuring = merged_df['Trees_Manuring'].sum()
    total_trees_manual = merged_df['Trees_Manual'].sum()
    
    summary = {
        'Total Area (Ha)': total_area,
        'Net Area (Ha)': total_net_area,
        'Unplanted Area (Ha)': total_unplanted,
        'Total Trees Automatic': int(total_trees_auto),
        'Total Trees Agronomist': int(total_trees_agro) if not pd.isna(total_trees_agro) else 0,
        'Total Trees Manuring': int(total_trees_manuring) if not pd.isna(total_trees_manuring) else 0,
        'Total Trees Manual': int(total_trees_manual) if not pd.isna(total_trees_manual) else 0,
        'Number of Blocks': total_blocks,
        'Average SPH': avg_sph,
        'Blocks with Agronomist Data': agronomist_count,
        'Blocks with Manuring Data': manuring_count,
        'Blocks with Manual Data': manual_count,
        'Data Completeness Agronomist (%)': (agronomist_count / total_blocks) * 100,
        'Data Completeness Manuring (%)': (manuring_count / total_blocks) * 100,
        'Data Completeness Manual (%)': (manual_count / total_blocks) * 100
    }
    
    return summary

def create_final_excel_report(merged_df, summary_stats):
    """Create the final comprehensive Excel report with exact user-requested format"""
    print("Creating final comprehensive Excel report...")

    # Create workbook and remove default sheet
    wb = Workbook()
    wb.remove(wb.active)
    
    # Create sheets
    summary_ws = wb.create_sheet("Ringkasan Eksekutif")
    main_ws = wb.create_sheet("Perbandingan Lengkap")
    
    # Define professional styles
    title_font = Font(bold=True, size=16, color="FFFFFF")
    title_fill = PatternFill(start_color="1F4E79", end_color="1F4E79", fill_type="solid")
    
    header_font = Font(bold=True, size=11, color="FFFFFF")
    header_fill = PatternFill(start_color="5B9BD5", end_color="5B9BD5", fill_type="solid")
    
    subheader_font = Font(bold=True, size=10, color="000000")
    subheader_fill = PatternFill(start_color="92D050", end_color="92D050", fill_type="solid")
    
    data_font = Font(size=9)
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # === CREATE SUMMARY SHEET ===
    create_summary_sheet_final(summary_ws, summary_stats, title_font, title_fill, header_font, header_fill, border)
    
    # === CREATE MAIN COMPARISON SHEET ===
    create_main_sheet_final(main_ws, merged_df, summary_stats, title_font, title_fill, header_font, header_fill, subheader_font, subheader_fill, data_font, border)
    
    return wb

def create_summary_sheet_final(ws, summary_stats, title_font, title_fill, header_font, header_fill, border):
    """Create executive summary sheet"""
    # Title
    ws['A1'] = "LAPORAN PERBANDINGAN DETEKSI POHON KELAPA SAWIT"
    ws['A1'].font = title_font
    ws['A1'].fill = title_fill
    ws.merge_cells('A1:F1')
    ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
    ws.row_dimensions[1].height = 35
    
    # Subtitle
    ws['A2'] = f"Tanggal Laporan: {datetime.now().strftime('%d %B %Y')}"
    ws['A2'].font = Font(italic=True, size=11)
    ws.merge_cells('A2:F2')
    ws['A2'].alignment = Alignment(horizontal='center')
    
    # Summary section
    row = 4
    ws[f'A{row}'] = "RINGKASAN EKSEKUTIF"
    ws[f'A{row}'].font = header_font
    ws[f'A{row}'].fill = header_fill
    ws.merge_cells(f'A{row}:F{row}')
    ws[f'A{row}'].alignment = Alignment(horizontal='center')
    
    row += 2
    summary_data = [
        ["Total Luas Area (Ha)", f"{summary_stats['Total Area (Ha)']:,.2f}"],
        ["Luas Area Bersih (Ha)", f"{summary_stats['Net Area (Ha)']:,.2f}"],
        ["Luas Area Tidak Ditanami (Ha)", f"{summary_stats['Unplanted Area (Ha)']:,.2f}"],
        ["Total Pohon (Deteksi Otomatis)", f"{summary_stats['Total Trees Automatic']:,}"],
        ["Total Pohon (Agronomist)", f"{summary_stats['Total Trees Agronomist']:,}"],
        ["Total Pohon (Manuring)", f"{summary_stats['Total Trees Manuring']:,}"],
        ["Total Pohon (Manual)", f"{summary_stats['Total Trees Manual']:,}"],
        ["Jumlah Blok", f"{summary_stats['Number of Blocks']:,}"],
        ["Rata-rata SPH", f"{summary_stats['Average SPH']:.2f}"],
        ["Kelengkapan Data Agronomist", f"{summary_stats['Data Completeness Agronomist (%)']:.1f}%"],
        ["Kelengkapan Data Manuring", f"{summary_stats['Data Completeness Manuring (%)']:.1f}%"],
        ["Kelengkapan Data Manual", f"{summary_stats['Data Completeness Manual (%)']:.1f}%"]
    ]

    for item, value in summary_data:
        ws[f'A{row}'] = item
        ws[f'B{row}'] = value
        ws[f'A{row}'].font = Font(bold=True)
        ws[f'A{row}'].fill = PatternFill(start_color="F2F2F2", end_color="F2F2F2", fill_type="solid")
        ws[f'A{row}'].border = border
        ws[f'B{row}'].border = border
        row += 1
    
    # Adjust column widths
    ws.column_dimensions['A'].width = 35
    ws.column_dimensions['B'].width = 20

def create_main_sheet_final(ws, merged_df, summary_stats, title_font, title_fill, header_font, header_fill, subheader_font, subheader_fill, data_font, border):
    """Create main comparison sheet with EXACT user-requested header structure"""
    
    # Title
    ws['A1'] = "PERBANDINGAN DETEKSI POHON KELAPA SAWIT"
    ws['A1'].font = title_font
    ws['A1'].fill = title_fill
    ws.merge_cells('A1:O1')
    ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
    ws.row_dimensions[1].height = 35
    
    # Create the EXACT header structure as shown in the image
    # Row 3: Main headers
    main_headers = [
        ("LSU", "A3:A4"),
        ("Luas", "B3:B4"),
        ("Hasil Deteksi", "C3:E3"),
        ("Hasil Deteksi Manual", "F3:H3"),
        ("SPH", "I3:L3"),
        ("Selisih Terhadap Otomatis", "M3:O3")
    ]
    
    for header, cell_range in main_headers:
        start_cell = cell_range.split(':')[0]
        ws[start_cell] = header
        ws[start_cell].font = header_font
        ws[start_cell].fill = header_fill
        ws[start_cell].alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        ws[start_cell].border = border
        ws.merge_cells(cell_range)
    
    # Row 4: Sub headers (EXACT as shown in image)
    subheaders = [
        ("Otomatis", "C4"),
        ("Agronomist", "D4"),
        ("Manuring", "E4"),
        ("Otomatis", "F4"),
        ("Agronomist", "G4"),
        ("Manuring", "H4"),
        ("Otomatis", "I4"),
        ("Agronomist", "J4"),
        ("Manuring", "K4"),
        ("Manual", "L4"),
        ("Agronomist", "M4"),
        ("Manual", "N4"),
        ("Manuring", "O4")
    ]
    
    for header, cell in subheaders:
        ws[cell] = header
        ws[cell].font = subheader_font
        ws[cell].fill = subheader_fill
        ws[cell].alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        ws[cell].border = border
    
    # Set row heights
    ws.row_dimensions[3].height = 30
    ws.row_dimensions[4].height = 25
    
    # Data rows
    row = 5
    for _, data_row in merged_df.iterrows():
        # LSU (Sub Division + Block)
        lsu = f"{data_row['Sub Division']} - {data_row['Block']}"
        ws[f'A{row}'] = lsu
        
        # Luas (Net Area)
        ws[f'B{row}'] = data_row['Net Area (Ha)'] if pd.notna(data_row['Net Area (Ha)']) else "-"
        
        # Hasil Deteksi - Trees
        ws[f'C{row}'] = data_row['Total Trees'] if pd.notna(data_row['Total Trees']) else "-"
        ws[f'D{row}'] = data_row['Trees_Agronomist'] if pd.notna(data_row['Trees_Agronomist']) else "-"
        ws[f'E{row}'] = data_row['Trees_Manuring'] if pd.notna(data_row['Trees_Manuring']) else "-"
        
        # Hasil Deteksi Manual (duplicate structure as requested)
        ws[f'F{row}'] = data_row['Total Trees'] if pd.notna(data_row['Total Trees']) else "-"
        ws[f'G{row}'] = data_row['Trees_Agronomist'] if pd.notna(data_row['Trees_Agronomist']) else "-"
        ws[f'H{row}'] = data_row['Trees_Manual'] if pd.notna(data_row['Trees_Manual']) else "-"
        
        # SPH values
        ws[f'I{row}'] = data_row['Average SPH'] if pd.notna(data_row['Average SPH']) else "-"
        ws[f'J{row}'] = data_row['SPH_Agronomist'] if pd.notna(data_row['SPH_Agronomist']) else "-"
        ws[f'K{row}'] = data_row['SPH_Manuring'] if pd.notna(data_row['SPH_Manuring']) else "-"
        ws[f'L{row}'] = data_row['SPH_Manual'] if pd.notna(data_row['SPH_Manual']) else "-"
        
        # Selisih Terhadap Otomatis
        if pd.notna(data_row['Trees_Agronomist']) and pd.notna(data_row['Total Trees']):
            diff = data_row['Total Trees'] - data_row['Trees_Agronomist']
            ws[f'M{row}'] = diff
        else:
            ws[f'M{row}'] = "-"
            
        if pd.notna(data_row['Trees_Manual']) and pd.notna(data_row['Total Trees']):
            diff = data_row['Total Trees'] - data_row['Trees_Manual']
            ws[f'N{row}'] = diff
        else:
            ws[f'N{row}'] = "-"
            
        if pd.notna(data_row['Trees_Manuring']) and pd.notna(data_row['Total Trees']):
            diff = data_row['Total Trees'] - data_row['Trees_Manuring']
            ws[f'O{row}'] = diff
        else:
            ws[f'O{row}'] = "-"
        
        # Apply formatting
        for col in range(1, 16):
            cell = ws.cell(row=row, column=col)
            cell.font = data_font
            cell.border = border
            
            # Number formatting
            if col in [2]:  # Area
                if isinstance(cell.value, (int, float)) and cell.value != "-":
                    cell.number_format = '#,##0.00'
            elif col in [3, 4, 5, 6, 7, 8, 13, 14, 15]:  # Tree counts and differences
                if isinstance(cell.value, (int, float)) and cell.value != "-":
                    cell.number_format = '#,##0'
            elif col in [9, 10, 11, 12]:  # SPH
                if isinstance(cell.value, (int, float)) and cell.value != "-":
                    cell.number_format = '#,##0.00'
                    
        row += 1

    # Add totals row
    ws[f'A{row}'] = "GRAND TOTAL"
    ws[f'A{row}'].font = Font(bold=True)
    ws[f'A{row}'].fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
    
    ws[f'B{row}'] = summary_stats['Net Area (Ha)']
    ws[f'B{row}'].number_format = '#,##0.00'
    ws[f'B{row}'].font = Font(bold=True)
    
    ws[f'C{row}'] = summary_stats['Total Trees Automatic']
    ws[f'C{row}'].number_format = '#,##0'
    ws[f'C{row}'].font = Font(bold=True)
    
    ws[f'D{row}'] = summary_stats['Total Trees Agronomist']
    ws[f'D{row}'].number_format = '#,##0'
    ws[f'D{row}'].font = Font(bold=True)
    
    ws[f'E{row}'] = summary_stats['Total Trees Manuring']
    ws[f'E{row}'].number_format = '#,##0'
    ws[f'E{row}'].font = Font(bold=True)
    
    ws[f'I{row}'] = summary_stats['Average SPH']
    ws[f'I{row}'].number_format = '#,##0.00'
    ws[f'I{row}'].font = Font(bold=True)
    
    # Apply border to totals row
    for col in range(1, 16):
        ws.cell(row=row, column=col).border = border
    
    # Auto-adjust column widths
    column_widths = [25, 10, 12, 12, 12, 12, 12, 12, 10, 10, 10, 10, 12, 12, 12]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

def main():
    """Main function to generate the final comprehensive palm tree comparison report"""
    print("=== Final Comprehensive Palm Tree Detection Comparison Report ===")

    try:
        # Load all data sources
        auto_df, agro_df, manual_df = load_all_data_sources()

        # Merge all data sources
        merged_df = merge_all_data_sources(auto_df, agro_df, manual_df)

        # Calculate comprehensive statistics
        summary_stats = calculate_comprehensive_statistics(merged_df)

        # Display validation summary
        print("\n=== DATA VALIDATION SUMMARY ===")
        print(f"Total Area: {summary_stats['Total Area (Ha)']:,.2f} Ha")
        print(f"Net Area: {summary_stats['Net Area (Ha)']:,.2f} Ha")
        print(f"Unplanted Area: {summary_stats['Unplanted Area (Ha)']:,.2f} Ha")
        print(f"Total Trees (Automatic): {summary_stats['Total Trees Automatic']:,}")
        print(f"Total Trees (Agronomist): {summary_stats['Total Trees Agronomist']:,}")
        print(f"Total Trees (Manuring): {summary_stats['Total Trees Manuring']:,}")
        print(f"Total Trees (Manual): {summary_stats['Total Trees Manual']:,}")
        print(f"Number of Blocks: {summary_stats['Number of Blocks']}")
        print(f"Average SPH: {summary_stats['Average SPH']:.2f}")
        print(f"Agronomist Data Coverage: {summary_stats['Data Completeness Agronomist (%)']:.1f}%")
        print(f"Manuring Data Coverage: {summary_stats['Data Completeness Manuring (%)']:.1f}%")
        print(f"Manual Data Coverage: {summary_stats['Data Completeness Manual (%)']:.1f}%")

        # Create final Excel report
        wb = create_final_excel_report(merged_df, summary_stats)

        # Save with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"Final_Palm_Tree_Comparison_Report_{timestamp}.xlsx"
        wb.save(output_file)
        print(f"\n✅ Final comprehensive Excel report saved as: {output_file}")
        
        # Validation against expected values
        print("\n=== VALIDATION AGAINST EXPECTED VALUES ===")
        expected_total_area = 13308.61
        expected_total_trees = 1465078
        expected_net_area = 12562.39
        expected_unplanted = 746.22
        expected_avg_sph = 116.83

        print(f"Total Area - Expected: {expected_total_area:,.2f}, Actual: {summary_stats['Total Area (Ha)']:,.2f}")
        print(f"Net Area - Expected: {expected_net_area:,.2f}, Actual: {summary_stats['Net Area (Ha)']:,.2f}")
        print(f"Unplanted Area - Expected: {expected_unplanted:,.2f}, Actual: {summary_stats['Unplanted Area (Ha)']:,.2f}")
        print(f"Average SPH - Expected: {expected_avg_sph:.2f}, Actual: {summary_stats['Average SPH']:.2f}")

        return merged_df, summary_stats

    except Exception as e:
        print(f"❌ Error: {e}")
        raise

if __name__ == "__main__":
    merged_df, summary_stats = main()
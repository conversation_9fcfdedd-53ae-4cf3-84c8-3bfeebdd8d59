#!/usr/bin/env python3
"""
FINAL CORRECTED Palm Tree Detection Comparison Report
Fixes ALL data parsing issues and adds Sub Division level analysis
Ensures grand totals match the exact required values
"""

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import re
from datetime import datetime

def clean_numeric_value_final(value):
    """FINAL CORRECTED numeric value parsing"""
    if pd.isna(value) or value == '' or value == '-':
        return None

    str_val = str(value).strip()
    if str_val == '-' or str_val == '':
        return None

    # CORRECTED LOGIC:
    # 1. Values like "657.0", "248.0", "970.0" are DECIMALS (not thousands)
    # 2. Values like "7.926", "8.528", "10.533" are THOUSANDS separators
    # 3. Values with comma are ALWAYS decimal separators
    
    if ',' in str_val and '.' in str_val:
        # European format: 1.234,56 - remove dots, comma becomes decimal
        str_val = str_val.replace('.', '').replace(',', '.')
    elif ',' in str_val:
        # Only comma - decimal separator
        str_val = str_val.replace(',', '.')
    elif '.' in str_val:
        # CRITICAL FIX: Better logic for dots
        parts = str_val.split('.')
        if len(parts) == 2:
            # Check if it's a thousands separator or decimal
            before_dot = parts[0]
            after_dot = parts[1]
            
            # If after dot is exactly 3 digits AND before dot is > 1 digit, it's thousands
            # E.g., "7.926" (1 digit . 3 digits) = thousands
            # E.g., "10.533" (2 digits . 3 digits) = thousands  
            # E.g., "657.0" (3 digits . 1 digit) = decimal
            # E.g., "248.0" (3 digits . 1 digit) = decimal
            
            if len(after_dot) == 3 and len(before_dot) <= 2:
                # Thousands separator: 7.926 -> 7926, 10.533 -> 10533
                str_val = str_val.replace('.', '')
            elif len(after_dot) == 1 and after_dot == '0':
                # Decimal with .0: 657.0 -> 657, 248.0 -> 248
                str_val = str_val.replace('.0', '')
            # Otherwise keep as decimal

    try:
        return float(str_val)
    except ValueError:
        return None

def standardize_block_name(block_name):
    """Standardize block names for matching"""
    if pd.isna(block_name):
        return ''
    
    block = str(block_name).strip().upper()
    block = re.sub(r'\s*/\s*', '/', block)
    block = re.sub(r'^P\s*', 'P ', block)
    
    return block

def load_automatic_detection_final():
    """Load automatic detection data with FINAL CORRECTED parsing"""
    print("Loading automatic detection data with FINAL corrected parsing...")
    
    df = pd.read_csv('data _luas_net_deteksi_otomatis.txt', sep='\t', encoding='utf-8')
    df.columns = df.columns.str.strip()
    
    # Apply FINAL corrected parsing
    df['Total Trees'] = df['Total Trees'].apply(lambda x: clean_numeric_value_final(x) if x != 0 else 0)
    
    # Areas and SPH
    for col in ['Total Area (Ha)', 'Net Area (Ha)', 'Unplanted Area (Ha)', 'Average SPH']:
        df[col] = df[col].apply(clean_numeric_value_final)
    
    # Standardize names for matching
    df['Block_Clean'] = df['Block'].apply(standardize_block_name)
    df['Match_Key'] = df['Sub Division'].str.strip() + '|' + df['Block_Clean']
    
    # Verification
    total_trees = df['Total Trees'].sum()
    print(f"Sample parsed values: {df['Total Trees'].head(10).tolist()}")
    print(f"FINAL Total Trees sum: {total_trees:,.0f}")
    print(f"Target: 1,465,078")
    print(f"Difference: {1465078 - total_trees:,.0f}")
    
    print(f"Loaded {len(df)} automatic detection records")
    return df

def load_agronomist_manuring_data():
    """Load agronomist and manuring data"""
    print("Loading agronomist and manuring data...")
    
    df = pd.read_csv('data_agronomist_manuring.txt', sep='\t', encoding='utf-8')
    df.columns = df.columns.str.strip()
    
    # Clean numeric columns
    numeric_cols = ['Hasil Agronomist', 'Hasil Manuring']
    for col in numeric_cols:
        df[col] = df[col].apply(clean_numeric_value_final)
    
    df['Block_Clean'] = df['BLOK'].apply(standardize_block_name)
    df['Match_Key'] = df['Sub Divisi'].str.strip() + '|' + df['Block_Clean']
    
    print(f"Loaded {len(df)} agronomist/manuring records")
    return df

def merge_all_data_sources_final(auto_df, agro_df):
    """Merge all data sources with final calculations"""
    print("Merging all data sources...")
    
    merged_df = auto_df.copy()
    
    # Initialize columns
    merged_df['Trees_Agronomist'] = None
    merged_df['Trees_Manuring'] = None
    merged_df['SPH_Agronomist'] = None
    merged_df['SPH_Manuring'] = None
    
    # Merge agronomist and manuring data
    agro_dict = agro_df.set_index('Match_Key').to_dict('index')
    
    for idx, row in merged_df.iterrows():
        match_key = row['Match_Key']
        if match_key in agro_dict:
            agro_data = agro_dict[match_key]
            
            # Agronomist data
            if agro_data['Hasil Agronomist'] is not None:
                merged_df.at[idx, 'Trees_Agronomist'] = agro_data['Hasil Agronomist']
                if row['Net Area (Ha)'] and row['Net Area (Ha)'] > 0:
                    merged_df.at[idx, 'SPH_Agronomist'] = agro_data['Hasil Agronomist'] / row['Net Area (Ha)']
            
            # Manuring data
            if agro_data['Hasil Manuring'] is not None:
                merged_df.at[idx, 'Trees_Manuring'] = agro_data['Hasil Manuring']
                if row['Net Area (Ha)'] and row['Net Area (Ha)'] > 0:
                    merged_df.at[idx, 'SPH_Manuring'] = agro_data['Hasil Manuring'] / row['Net Area (Ha)']
    
    print(f"Merged dataset contains {len(merged_df)} records")
    return merged_df

def calculate_subdivision_analysis(merged_df):
    """Calculate Sub Division level analysis"""
    print("Calculating Sub Division level analysis...")
    
    # Group by Sub Division
    subdivision_summary = []
    
    for sub_div in merged_df['Sub Division'].unique():
        sub_data = merged_df[merged_df['Sub Division'] == sub_div].copy()
        
        # Calculate totals for this Sub Division
        total_area = sub_data['Total Area (Ha)'].sum()
        net_area = sub_data['Net Area (Ha)'].sum()
        unplanted_area = sub_data['Unplanted Area (Ha)'].sum()
        
        # Trees totals
        trees_auto = sub_data['Total Trees'].sum()
        trees_agro = sub_data['Trees_Agronomist'].sum()
        trees_manuring = sub_data['Trees_Manuring'].sum()
        
        # SPH calculations
        sph_auto = trees_auto / net_area if net_area > 0 else 0
        sph_agro = trees_agro / net_area if net_area > 0 and not pd.isna(trees_agro) else None
        sph_manuring = trees_manuring / net_area if net_area > 0 and not pd.isna(trees_manuring) else None
        
        # Data completeness
        total_blocks = len(sub_data)
        agro_blocks = sub_data['Trees_Agronomist'].notna().sum()
        manuring_blocks = sub_data['Trees_Manuring'].notna().sum()
        
        subdivision_summary.append({
            'Sub Division': sub_div,
            'Total Blocks': total_blocks,
            'Total Area (Ha)': total_area,
            'Net Area (Ha)': net_area,
            'Unplanted Area (Ha)': unplanted_area,
            'Trees_Automatic': int(trees_auto),
            'Trees_Agronomist': int(trees_agro) if not pd.isna(trees_agro) else 0,
            'Trees_Manuring': int(trees_manuring) if not pd.isna(trees_manuring) else 0,
            'SPH_Automatic': sph_auto,
            'SPH_Agronomist': sph_agro,
            'SPH_Manuring': sph_manuring,
            'Agro_Blocks': agro_blocks,
            'Manuring_Blocks': manuring_blocks,
            'Agro_Coverage (%)': (agro_blocks / total_blocks) * 100,
            'Manuring_Coverage (%)': (manuring_blocks / total_blocks) * 100
        })
    
    return pd.DataFrame(subdivision_summary)

def calculate_final_statistics(merged_df, subdivision_df):
    """Calculate final summary statistics"""
    print("Calculating final summary statistics...")
    
    # Basic totals
    total_area = merged_df['Total Area (Ha)'].sum()
    total_net_area = merged_df['Net Area (Ha)'].sum()
    total_unplanted = merged_df['Unplanted Area (Ha)'].sum()
    total_trees_auto = merged_df['Total Trees'].sum()
    total_blocks = len(merged_df)
    
    # Calculate average SPH
    avg_sph = total_trees_auto / total_net_area if total_net_area > 0 else 0
    
    # Count data availability
    agronomist_count = merged_df['Trees_Agronomist'].notna().sum()
    manuring_count = merged_df['Trees_Manuring'].notna().sum()
    
    # Calculate totals for each method
    total_trees_agro = merged_df['Trees_Agronomist'].sum()
    total_trees_manuring = merged_df['Trees_Manuring'].sum()
    
    summary = {
        'Total Area (Ha)': total_area,
        'Net Area (Ha)': total_net_area,
        'Unplanted Area (Ha)': total_unplanted,
        'Total Trees Automatic': int(total_trees_auto),
        'Total Trees Agronomist': int(total_trees_agro) if not pd.isna(total_trees_agro) else 0,
        'Total Trees Manuring': int(total_trees_manuring) if not pd.isna(total_trees_manuring) else 0,
        'Number of Blocks': total_blocks,
        'Average SPH': avg_sph,
        'Blocks with Agronomist Data': agronomist_count,
        'Blocks with Manuring Data': manuring_count,
        'Data Completeness Agronomist (%)': (agronomist_count / total_blocks) * 100,
        'Data Completeness Manuring (%)': (manuring_count / total_blocks) * 100,
        'Number of Sub Divisions': len(subdivision_df)
    }
    
    return summary

def create_final_excel_report(merged_df, subdivision_df, summary_stats):
    """Create final Excel report with all requirements met"""
    print("Creating final comprehensive Excel report...")

    # Create workbook
    wb = Workbook()
    wb.remove(wb.active)
    
    # Create sheets
    summary_ws = wb.create_sheet("📊 Ringkasan Eksekutif")
    subdivision_ws = wb.create_sheet("📈 Analisis Sub Division")
    main_ws = wb.create_sheet("📋 Detail per Blok")
    
    # Define styles
    title_font = Font(bold=True, size=16, color="FFFFFF")
    title_fill = PatternFill(start_color="1F4E79", end_color="1F4E79", fill_type="solid")
    
    header_font = Font(bold=True, size=11, color="FFFFFF")
    header_fill = PatternFill(start_color="5B9BD5", end_color="5B9BD5", fill_type="solid")
    
    subheader_font = Font(bold=True, size=10, color="000000")
    subheader_fill = PatternFill(start_color="92D050", end_color="92D050", fill_type="solid")
    
    data_font = Font(size=9)
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # Create all sheets
    create_final_executive_summary(summary_ws, summary_stats, title_font, title_fill, header_font, header_fill, border)
    create_final_subdivision_analysis(subdivision_ws, subdivision_df, title_font, title_fill, header_font, header_fill, data_font, border)
    create_final_detail_blocks(main_ws, merged_df, summary_stats, title_font, title_fill, header_font, header_fill, subheader_font, subheader_fill, data_font, border)
    
    return wb

def create_final_executive_summary(ws, summary_stats, title_font, title_fill, header_font, header_fill, border):
    """Create final executive summary sheet"""
    # Title
    ws['A1'] = "LAPORAN PERBANDINGAN DETEKSI POHON KELAPA SAWIT - FINAL"
    ws['A1'].font = title_font
    ws['A1'].fill = title_fill
    ws.merge_cells('A1:F1')
    ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
    ws.row_dimensions[1].height = 35
    
    # Date
    ws['A2'] = f"Tanggal Laporan: {datetime.now().strftime('%d %B %Y, %H:%M WIB')}"
    ws['A2'].font = Font(italic=True, size=11)
    ws.merge_cells('A2:F2')
    ws['A2'].alignment = Alignment(horizontal='center')
    
    # Summary section
    row = 4
    ws[f'A{row}'] = "RINGKASAN EKSEKUTIF"
    ws[f'A{row}'].font = header_font
    ws[f'A{row}'].fill = header_fill
    ws.merge_cells(f'A{row}:F{row}')
    ws[f'A{row}'].alignment = Alignment(horizontal='center')
    
    row += 2
    summary_data = [
        ["📊 Total Luas Area (Ha)", f"{summary_stats['Total Area (Ha)']:,.2f}"],
        ["🌱 Luas Area Bersih (Ha)", f"{summary_stats['Net Area (Ha)']:,.2f}"],
        ["🚫 Luas Area Tidak Ditanami (Ha)", f"{summary_stats['Unplanted Area (Ha)']:,.2f}"],
        ["🌴 Total Pohon (Deteksi Otomatis)", f"{summary_stats['Total Trees Automatic']:,}"],
        ["👨‍🌾 Total Pohon (Agronomist)", f"{summary_stats['Total Trees Agronomist']:,}"],
        ["🔧 Total Pohon (Manuring)", f"{summary_stats['Total Trees Manuring']:,}"],
        ["📍 Jumlah Blok", f"{summary_stats['Number of Blocks']:,}"],
        ["🏗️ Jumlah Sub Division", f"{summary_stats['Number of Sub Divisions']:,}"],
        ["📈 Rata-rata SPH", f"{summary_stats['Average SPH']:.2f}"],
        ["✅ Kelengkapan Data Agronomist", f"{summary_stats['Data Completeness Agronomist (%)']:.1f}%"],
        ["🔧 Kelengkapan Data Manuring", f"{summary_stats['Data Completeness Manuring (%)']:.1f}%"]
    ]

    for item, value in summary_data:
        ws[f'A{row}'] = item
        ws[f'B{row}'] = value
        ws[f'A{row}'].font = Font(bold=True)
        ws[f'A{row}'].fill = PatternFill(start_color="F2F2F2", end_color="F2F2F2", fill_type="solid")
        ws[f'A{row}'].border = border
        ws[f'B{row}'].border = border
        row += 1
    
    # Validation section
    row += 2
    ws[f'A{row}'] = "✅ VALIDASI DATA TERHADAP TARGET"
    ws[f'A{row}'].font = header_font
    ws[f'A{row}'].fill = header_fill
    ws.merge_cells(f'A{row}:F{row}')
    ws[f'A{row}'].alignment = Alignment(horizontal='center')
    
    row += 2
    expected_data = [
        ["Total Area Target", "13,308.61 Ha", f"{summary_stats['Total Area (Ha)']:,.2f} Ha", "✅" if abs(summary_stats['Total Area (Ha)'] - 13308.61) < 0.01 else "❌"],
        ["Total Trees Target", "1,465,078", f"{summary_stats['Total Trees Automatic']:,}", "✅" if summary_stats['Total Trees Automatic'] == 1465078 else "❌"],
        ["Net Area Target", "12,562.39 Ha", f"{summary_stats['Net Area (Ha)']:,.2f} Ha", "✅" if abs(summary_stats['Net Area (Ha)'] - 12562.39) < 0.01 else "❌"],
        ["Unplanted Area Target", "746.22 Ha", f"{summary_stats['Unplanted Area (Ha)']:,.2f} Ha", "✅" if abs(summary_stats['Unplanted Area (Ha)'] - 746.22) < 0.01 else "❌"],
        ["Average SPH Target", "118.38", f"{summary_stats['Average SPH']:.2f}", "✅" if abs(summary_stats['Average SPH'] - 118.38) < 0.1 else "❌"]
    ]
    
    # Headers for validation
    validation_headers = ["Metrik", "Target", "Aktual", "Status"]
    for col, header in enumerate(validation_headers, 1):
        ws.cell(row=row, column=col, value=header).font = Font(bold=True)
        ws.cell(row=row, column=col).fill = PatternFill(start_color="E7E6E6", end_color="E7E6E6", fill_type="solid")
        ws.cell(row=row, column=col).border = border
    
    row += 1
    for item, expected, actual, status in expected_data:
        ws[f'A{row}'] = item
        ws[f'B{row}'] = expected
        ws[f'C{row}'] = actual
        ws[f'D{row}'] = status
        for col in range(1, 5):
            ws.cell(row=row, column=col).border = border
        row += 1
    
    # Adjust column widths
    ws.column_dimensions['A'].width = 35
    ws.column_dimensions['B'].width = 20
    ws.column_dimensions['C'].width = 20
    ws.column_dimensions['D'].width = 10

def create_final_subdivision_analysis(ws, subdivision_df, title_font, title_fill, header_font, header_fill, data_font, border):
    """Create final Sub Division analysis sheet"""
    # Title
    ws['A1'] = "ANALISIS TINGKAT SUB DIVISION"
    ws['A1'].font = title_font
    ws['A1'].fill = title_fill
    ws.merge_cells('A1:O1')
    ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
    ws.row_dimensions[1].height = 35
    
    # Headers
    headers = [
        "Sub Division", "Blocks", "Total Area", "Net Area", "Unplanted", 
        "Trees Auto", "Trees Agro", "Trees Manur", 
        "SPH Auto", "SPH Agro", "SPH Manur",
        "Agro Blocks", "Manur Blocks", "Agro %", "Manur %"
    ]
    
    row = 3
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=row, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = border
        cell.alignment = Alignment(horizontal='center', wrap_text=True)
    
    # Data rows
    row = 4
    for _, sub_data in subdivision_df.iterrows():
        ws[f'A{row}'] = sub_data['Sub Division']
        ws[f'B{row}'] = sub_data['Total Blocks']
        ws[f'C{row}'] = sub_data['Total Area (Ha)']
        ws[f'D{row}'] = sub_data['Net Area (Ha)']
        ws[f'E{row}'] = sub_data['Unplanted Area (Ha)']
        ws[f'F{row}'] = sub_data['Trees_Automatic']
        ws[f'G{row}'] = sub_data['Trees_Agronomist'] if sub_data['Trees_Agronomist'] > 0 else "-"
        ws[f'H{row}'] = sub_data['Trees_Manuring'] if sub_data['Trees_Manuring'] > 0 else "-"
        ws[f'I{row}'] = sub_data['SPH_Automatic']
        ws[f'J{row}'] = sub_data['SPH_Agronomist'] if pd.notna(sub_data['SPH_Agronomist']) else "-"
        ws[f'K{row}'] = sub_data['SPH_Manuring'] if pd.notna(sub_data['SPH_Manuring']) else "-"
        ws[f'L{row}'] = sub_data['Agro_Blocks']
        ws[f'M{row}'] = sub_data['Manuring_Blocks']
        ws[f'N{row}'] = f"{sub_data['Agro_Coverage (%)']:.1f}%"
        ws[f'O{row}'] = f"{sub_data['Manuring_Coverage (%)']:.1f}%"
        
        # Format and borders
        for col in range(1, 16):
            cell = ws.cell(row=row, column=col)
            cell.font = data_font
            cell.border = border
            
            # Number formatting
            if col in [3, 4, 5]:  # Areas
                if isinstance(cell.value, (int, float)):
                    cell.number_format = '#,##0.00'
            elif col in [6, 7, 8]:  # Tree counts
                if isinstance(cell.value, (int, float)):
                    cell.number_format = '#,##0'
            elif col in [9, 10, 11]:  # SPH
                if isinstance(cell.value, (int, float)):
                    cell.number_format = '#,##0.00'
                    
        row += 1
    
    # Add totals row
    ws[f'A{row}'] = "GRAND TOTAL"
    ws[f'A{row}'].font = Font(bold=True)
    ws[f'B{row}'] = subdivision_df['Total Blocks'].sum()
    ws[f'C{row}'] = subdivision_df['Total Area (Ha)'].sum()
    ws[f'D{row}'] = subdivision_df['Net Area (Ha)'].sum()
    ws[f'E{row}'] = subdivision_df['Unplanted Area (Ha)'].sum()
    ws[f'F{row}'] = subdivision_df['Trees_Automatic'].sum()
    ws[f'G{row}'] = subdivision_df['Trees_Agronomist'].sum()
    ws[f'H{row}'] = subdivision_df['Trees_Manuring'].sum()
    
    for col in range(1, 16):
        ws.cell(row=row, column=col).border = border
        if col >= 2:
            ws.cell(row=row, column=col).font = Font(bold=True)
    
    # Auto-adjust column widths
    column_widths = [30, 8, 12, 12, 12, 12, 12, 12, 10, 10, 10, 8, 8, 8, 8]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

def create_final_detail_blocks(ws, merged_df, summary_stats, title_font, title_fill, header_font, header_fill, subheader_font, subheader_fill, data_font, border):
    """Create final detailed blocks sheet"""
    # Title
    ws['A1'] = "DETAIL PERBANDINGAN PER BLOK"
    ws['A1'].font = title_font
    ws['A1'].fill = title_fill
    ws.merge_cells('A1:M1')
    ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
    ws.row_dimensions[1].height = 35
    
    # Headers
    main_headers = [
        ("Sub Division", "A3:A4"),
        ("Block", "B3:B4"),
        ("Luas (Ha)", "C3:C4"),
        ("Hasil Deteksi", "D3:F3"),
        ("SPH", "G3:I3"),
        ("Selisih vs Otomatis", "J3:M3")
    ]
    
    for header, cell_range in main_headers:
        start_cell = cell_range.split(':')[0]
        ws[start_cell] = header
        ws[start_cell].font = header_font
        ws[start_cell].fill = header_fill
        ws[start_cell].alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        ws[start_cell].border = border
        ws.merge_cells(cell_range)
    
    # Sub headers
    subheaders = [
        ("Otomatis", "D4"),
        ("Agronomist", "E4"),
        ("Manuring", "F4"),
        ("Otomatis", "G4"),
        ("Agronomist", "H4"),
        ("Manuring", "I4"),
        ("Agro (Abs)", "J4"),
        ("Agro (%)", "K4"),
        ("Manur (Abs)", "L4"),
        ("Manur (%)", "M4")
    ]
    
    for header, cell in subheaders:
        ws[cell] = header
        ws[cell].font = subheader_font
        ws[cell].fill = subheader_fill
        ws[cell].alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        ws[cell].border = border
    
    # Set row heights
    ws.row_dimensions[3].height = 25
    ws.row_dimensions[4].height = 25
    
    # Data rows
    row = 5
    for _, data_row in merged_df.iterrows():
        ws[f'A{row}'] = data_row['Sub Division']
        ws[f'B{row}'] = data_row['Block']
        ws[f'C{row}'] = data_row['Net Area (Ha)'] if pd.notna(data_row['Net Area (Ha)']) else "-"
        
        # Tree counts
        ws[f'D{row}'] = data_row['Total Trees'] if pd.notna(data_row['Total Trees']) else "-"
        ws[f'E{row}'] = data_row['Trees_Agronomist'] if pd.notna(data_row['Trees_Agronomist']) else "-"
        ws[f'F{row}'] = data_row['Trees_Manuring'] if pd.notna(data_row['Trees_Manuring']) else "-"
        
        # SPH values
        ws[f'G{row}'] = data_row['Average SPH'] if pd.notna(data_row['Average SPH']) else "-"
        ws[f'H{row}'] = data_row['SPH_Agronomist'] if pd.notna(data_row['SPH_Agronomist']) else "-"
        ws[f'I{row}'] = data_row['SPH_Manuring'] if pd.notna(data_row['SPH_Manuring']) else "-"
        
        # Differences
        if pd.notna(data_row['Trees_Agronomist']) and pd.notna(data_row['Total Trees']):
            diff = data_row['Total Trees'] - data_row['Trees_Agronomist']
            pct_diff = (diff / data_row['Trees_Agronomist']) * 100 if data_row['Trees_Agronomist'] != 0 else 0
            ws[f'J{row}'] = diff
            ws[f'K{row}'] = f"{pct_diff:.1f}%"
        else:
            ws[f'J{row}'] = "-"
            ws[f'K{row}'] = "-"
            
        if pd.notna(data_row['Trees_Manuring']) and pd.notna(data_row['Total Trees']):
            diff = data_row['Total Trees'] - data_row['Trees_Manuring']
            pct_diff = (diff / data_row['Trees_Manuring']) * 100 if data_row['Trees_Manuring'] != 0 else 0
            ws[f'L{row}'] = diff
            ws[f'M{row}'] = f"{pct_diff:.1f}%"
        else:
            ws[f'L{row}'] = "-"
            ws[f'M{row}'] = "-"
        
        # Apply formatting
        for col in range(1, 14):
            cell = ws.cell(row=row, column=col)
            cell.font = data_font
            cell.border = border
            
            if col in [3]:  # Area
                if isinstance(cell.value, (int, float)):
                    cell.number_format = '#,##0.00'
            elif col in [4, 5, 6, 10, 12]:  # Tree counts and differences
                if isinstance(cell.value, (int, float)):
                    cell.number_format = '#,##0'
            elif col in [7, 8, 9]:  # SPH
                if isinstance(cell.value, (int, float)):
                    cell.number_format = '#,##0.00'
                    
        row += 1
    
    # Auto-adjust column widths
    column_widths = [25, 15, 10, 12, 12, 12, 10, 10, 10, 12, 10, 12, 10]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

def main():
    """Main function with FINAL corrected data parsing and complete Sub Division analysis"""
    print("=== FINAL CORRECTED Palm Tree Detection Comparison Report ===")

    try:
        # Load data with FINAL corrected parsing
        auto_df = load_automatic_detection_final()
        agro_df = load_agronomist_manuring_data()

        # Merge data
        merged_df = merge_all_data_sources_final(auto_df, agro_df)

        # Calculate Sub Division analysis
        subdivision_df = calculate_subdivision_analysis(merged_df)

        # Calculate final statistics
        summary_stats = calculate_final_statistics(merged_df, subdivision_df)

        # Display validation summary
        print("\n=== FINAL DATA VALIDATION SUMMARY ===")
        print(f"Total Area: {summary_stats['Total Area (Ha)']:,.2f} Ha")
        print(f"Net Area: {summary_stats['Net Area (Ha)']:,.2f} Ha") 
        print(f"Unplanted Area: {summary_stats['Unplanted Area (Ha)']:,.2f} Ha")
        print(f"Total Trees (Automatic): {summary_stats['Total Trees Automatic']:,}")
        print(f"Total Trees (Agronomist): {summary_stats['Total Trees Agronomist']:,}")
        print(f"Total Trees (Manuring): {summary_stats['Total Trees Manuring']:,}")
        print(f"Number of Blocks: {summary_stats['Number of Blocks']}")
        print(f"Number of Sub Divisions: {summary_stats['Number of Sub Divisions']}")
        print(f"Average SPH: {summary_stats['Average SPH']:.2f}")

        # Validation against expected values
        print("\n=== FINAL VALIDATION AGAINST EXPECTED VALUES ===")
        expected_values = {
            'Total Area': 13308.61,
            'Total Trees': 1465078,
            'Net Area': 12562.39,
            'Unplanted Area': 746.22,
            'Average SPH': 118.38
        }

        validation_results = {}
        all_passed = True
        
        for metric, expected in expected_values.items():
            actual_key = {
                'Total Area': 'Total Area (Ha)',
                'Total Trees': 'Total Trees Automatic', 
                'Net Area': 'Net Area (Ha)',
                'Unplanted Area': 'Unplanted Area (Ha)',
                'Average SPH': 'Average SPH'
            }[metric]
            
            actual = summary_stats[actual_key]
            
            if metric == 'Total Trees':
                passed = actual == expected
            else:
                passed = abs(actual - expected) < 0.1
                
            validation_results[metric] = passed
            if not passed:
                all_passed = False
                
            status = "✅" if passed else "❌"
            print(f"{metric} - Expected: {expected:,.2f}, Actual: {actual:,.2f} {status}")

        # Create final Excel report
        wb = create_final_excel_report(merged_df, subdivision_df, summary_stats)

        # Save with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"FINAL_Palm_Tree_Comparison_Report_{timestamp}.xlsx"
        wb.save(output_file)
        
        if all_passed:
            print(f"\n🎉 ✅ ALL VALIDATIONS PASSED! Final comprehensive Excel report saved as: {output_file}")
        else:
            print(f"\n⚠️ Some validations failed. Report saved as: {output_file}")

        return merged_df, subdivision_df, summary_stats

    except Exception as e:
        print(f"❌ Error: {e}")
        raise

if __name__ == "__main__":
    merged_df, subdivision_df, summary_stats = main()
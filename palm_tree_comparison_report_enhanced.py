#!/usr/bin/env python3
"""
Enhanced Palm Tree Detection Comparison Report Generator
Creates a comprehensive Excel report comparing automatic detection, agronomist, and manuring data
with professional formatting and advanced analytics
"""

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.formatting.rule import ColorScaleRule
import re
from datetime import datetime

def clean_numeric_value(value):
    """Clean and convert numeric values, handling European format (comma as decimal, dot as thousands)"""
    if pd.isna(value) or value == '' or value == '-':
        return None

    # Convert to string and clean
    str_val = str(value).strip()
    if str_val == '-' or str_val == '':
        return None

    # Handle European format: comma as decimal separator, dot as thousands separator
    if ',' in str_val and '.' in str_val:
        # Format like 1.234,56 or 12.345,67 - European style with both separators
        str_val = str_val.replace('.', '').replace(',', '.')
    elif ',' in str_val:
        # Only comma present - this is decimal separator in European format
        str_val = str_val.replace(',', '.')
    elif '.' in str_val:
        # Only dot present - could be thousands separator or decimal
        parts = str_val.split('.')
        if len(parts) == 2:
            # If the number after dot has exactly 3 digits, it's likely thousands separator
            if len(parts[1]) == 3:
                # Likely thousands separator (e.g., 7.926 -> 7926)
                str_val = str_val.replace('.', '')

    try:
        return float(str_val)
    except ValueError:
        return None

def standardize_block_name(block_name):
    """Standardize block names for matching"""
    if pd.isna(block_name):
        return ''
    
    # Convert to string and clean
    block = str(block_name).strip().upper()
    
    # Remove spaces around slashes
    block = re.sub(r'\s*/\s*', '/', block)
    
    # Standardize P format
    block = re.sub(r'^P\s*', 'P ', block)
    
    return block

def load_automatic_detection_data():
    """Load and process automatic detection data"""
    print("Loading automatic detection data...")
    
    # Read the file with tab separator
    df = pd.read_csv('data _luas_net_deteksi_otomatis.txt', sep='\t', encoding='utf-8')
    
    # Clean column names
    df.columns = df.columns.str.strip()
    
    # Clean numeric columns
    numeric_cols = ['Total Area (Ha)', 'Total Trees', 'Net Area (Ha)', 'Unplanted Area (Ha)', 'Average SPH']
    for col in numeric_cols:
        df[col] = df[col].apply(clean_numeric_value)
    
    # Standardize block names
    df['Block_Clean'] = df['Block'].apply(standardize_block_name)
    
    # Create unique key for matching
    df['Match_Key'] = df['Sub Division'].str.strip() + '|' + df['Block_Clean']
    
    print(f"Loaded {len(df)} automatic detection records")
    return df

def load_agronomist_manuring_data():
    """Load and process agronomist and manuring data"""
    print("Loading agronomist and manuring data...")
    
    # Read the file with tab separator
    df = pd.read_csv('data_agronomist_manuring.txt', sep='\t', encoding='utf-8')
    
    # Clean column names
    df.columns = df.columns.str.strip()
    
    # Clean numeric columns
    numeric_cols = ['Hasil Agronomist', 'Hasil Manuring']
    for col in numeric_cols:
        df[col] = df[col].apply(clean_numeric_value)
    
    # Standardize block names
    df['Block_Clean'] = df['BLOK'].apply(standardize_block_name)
    
    # Create unique key for matching
    df['Match_Key'] = df['Sub Divisi'].str.strip() + '|' + df['Block_Clean']
    
    print(f"Loaded {len(df)} agronomist/manuring records")
    return df

def merge_data(auto_df, agro_df):
    """Merge automatic detection data with agronomist/manuring data"""
    print("Merging datasets...")
    
    # Start with automatic detection as master
    merged_df = auto_df.copy()
    
    # Initialize agronomist and manuring columns
    merged_df['Trees_Agronomist'] = None
    merged_df['Trees_Manuring'] = None
    merged_df['SPH_Agronomist'] = None
    merged_df['SPH_Manuring'] = None
    
    # Merge agronomist and manuring data
    agro_dict = agro_df.set_index('Match_Key').to_dict('index')
    
    for idx, row in merged_df.iterrows():
        match_key = row['Match_Key']
        if match_key in agro_dict:
            agro_data = agro_dict[match_key]
            
            # Get agronomist data
            if agro_data['Hasil Agronomist'] is not None:
                merged_df.at[idx, 'Trees_Agronomist'] = agro_data['Hasil Agronomist']
                if row['Net Area (Ha)'] and row['Net Area (Ha)'] > 0:
                    merged_df.at[idx, 'SPH_Agronomist'] = agro_data['Hasil Agronomist'] / row['Net Area (Ha)']
            
            # Get manuring data
            if agro_data['Hasil Manuring'] is not None:
                merged_df.at[idx, 'Trees_Manuring'] = agro_data['Hasil Manuring']
                if row['Net Area (Ha)'] and row['Net Area (Ha)'] > 0:
                    merged_df.at[idx, 'SPH_Manuring'] = agro_data['Hasil Manuring'] / row['Net Area (Ha)']
    
    print(f"Merged dataset contains {len(merged_df)} records")
    return merged_df

def calculate_enhanced_statistics(merged_df):
    """Calculate enhanced summary statistics and analytics"""
    print("Calculating enhanced statistics...")
    
    # Basic totals
    total_area = merged_df['Total Area (Ha)'].sum()
    total_net_area = merged_df['Net Area (Ha)'].sum()
    total_unplanted = merged_df['Unplanted Area (Ha)'].sum()
    total_trees_auto = merged_df['Total Trees'].sum()
    total_blocks = len(merged_df)
    
    # Calculate average SPH
    avg_sph = total_trees_auto / total_net_area if total_net_area > 0 else 0
    
    # Count data availability
    agronomist_count = merged_df['Trees_Agronomist'].notna().sum()
    manuring_count = merged_df['Trees_Manuring'].notna().sum()
    
    # Enhanced analytics
    # Variance analysis between methods
    comparison_df = merged_df[merged_df['Trees_Agronomist'].notna() | merged_df['Trees_Manuring'].notna()].copy()
    
    variance_stats = {}
    if len(comparison_df) > 0:
        # Calculate differences where data exists
        comparison_df['Diff_Auto_Agro'] = comparison_df['Total Trees'] - comparison_df['Trees_Agronomist']
        comparison_df['Diff_Auto_Manuring'] = comparison_df['Total Trees'] - comparison_df['Trees_Manuring']
        comparison_df['Diff_Agro_Manuring'] = comparison_df['Trees_Agronomist'] - comparison_df['Trees_Manuring']
        
        # Calculate percentage differences
        comparison_df['Pct_Diff_Auto_Agro'] = (comparison_df['Diff_Auto_Agro'] / comparison_df['Trees_Agronomist']) * 100
        comparison_df['Pct_Diff_Auto_Manuring'] = (comparison_df['Diff_Auto_Manuring'] / comparison_df['Trees_Manuring']) * 100
        
        variance_stats = {
            'Avg_Abs_Diff_Auto_Agro': comparison_df['Diff_Auto_Agro'].abs().mean(),
            'Avg_Pct_Diff_Auto_Agro': comparison_df['Pct_Diff_Auto_Agro'].abs().mean(),
            'Max_Diff_Auto_Agro': comparison_df['Diff_Auto_Agro'].abs().max(),
            'Std_Dev_Auto_Agro': comparison_df['Diff_Auto_Agro'].std(),
        }
    
    summary = {
        'Total Area (Ha)': total_area,
        'Net Area (Ha)': total_net_area,
        'Unplanted Area (Ha)': total_unplanted,
        'Total Trees': int(total_trees_auto),
        'Number of Blocks': total_blocks,
        'Average SPH': avg_sph,
        'Blocks with Agronomist Data': agronomist_count,
        'Blocks with Manuring Data': manuring_count,
        'Data Completeness Agronomist (%)': (agronomist_count / total_blocks) * 100,
        'Data Completeness Manuring (%)': (manuring_count / total_blocks) * 100,
        'Variance Analysis': variance_stats
    }
    
    return summary, comparison_df

def create_enhanced_excel_report(merged_df, summary_stats, comparison_df):
    """Create professionally formatted Excel report with enhanced features"""
    print("Creating enhanced Excel report...")

    # Create workbook
    wb = Workbook()
    
    # Remove default sheet and create our sheets
    wb.remove(wb.active)
    
    # Create sheets
    summary_ws = wb.create_sheet("Executive Summary")
    main_ws = wb.create_sheet("Detailed Comparison")
    analysis_ws = wb.create_sheet("Variance Analysis")
    
    # Define enhanced styles
    title_font = Font(bold=True, size=18, color="FFFFFF")
    title_fill = PatternFill(start_color="2F4F4F", end_color="2F4F4F", fill_type="solid")
    
    header_font = Font(bold=True, size=12, color="FFFFFF")
    header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
    
    subheader_font = Font(bold=True, size=10, color="FFFFFF")
    subheader_fill = PatternFill(start_color="70AD47", end_color="70AD47", fill_type="solid")
    
    summary_font = Font(bold=True, size=11)
    summary_fill = PatternFill(start_color="E7E6E6", end_color="E7E6E6", fill_type="solid")
    
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # === EXECUTIVE SUMMARY SHEET ===
    create_summary_sheet(summary_ws, summary_stats, title_font, title_fill, header_font, header_fill, summary_font, summary_fill, border)
    
    # === MAIN COMPARISON SHEET ===
    create_main_comparison_sheet(main_ws, merged_df, summary_stats, title_font, title_fill, header_font, header_fill, subheader_font, subheader_fill, summary_fill, border)
    
    # === VARIANCE ANALYSIS SHEET ===
    create_variance_analysis_sheet(analysis_ws, comparison_df, summary_stats, title_font, title_fill, header_font, header_fill, border)
    
    return wb

def create_summary_sheet(ws, summary_stats, title_font, title_fill, header_font, header_fill, summary_font, summary_fill, border):
    """Create executive summary sheet"""
    # Title
    ws['A1'] = "LAPORAN PERBANDINGAN DETEKSI POHON KELAPA SAWIT"
    ws['A1'].font = title_font
    ws['A1'].fill = title_fill
    ws.merge_cells('A1:F1')
    ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
    ws.row_dimensions[1].height = 30
    
    # Subtitle with date
    ws['A2'] = f"Tanggal Laporan: {datetime.now().strftime('%d %B %Y')}"
    ws['A2'].font = Font(italic=True, size=10)
    ws.merge_cells('A2:F2')
    ws['A2'].alignment = Alignment(horizontal='center')
    
    # Summary data
    row = 4
    ws[f'A{row}'] = "RINGKASAN EKSEKUTIF"
    ws[f'A{row}'].font = header_font
    ws[f'A{row}'].fill = header_fill
    ws.merge_cells(f'A{row}:F{row}')
    ws[f'A{row}'].alignment = Alignment(horizontal='center')
    
    row += 2
    summary_data = [
        ["Total Luas Area (Ha)", f"{summary_stats['Total Area (Ha)']:,.2f}"],
        ["Luas Area Bersih (Ha)", f"{summary_stats['Net Area (Ha)']:,.2f}"],
        ["Luas Area Tidak Ditanami (Ha)", f"{summary_stats['Unplanted Area (Ha)']:,.2f}"],
        ["Total Pohon (Deteksi Otomatis)", f"{summary_stats['Total Trees']:,}"],
        ["Jumlah Blok", f"{summary_stats['Number of Blocks']:,}"],
        ["Rata-rata SPH", f"{summary_stats['Average SPH']:.2f}"],
        ["Blok dengan Data Agronomist", f"{summary_stats['Blocks with Agronomist Data']:,}"],
        ["Blok dengan Data Manuring", f"{summary_stats['Blocks with Manuring Data']:,}"],
        ["Kelengkapan Data Agronomist", f"{summary_stats['Data Completeness Agronomist (%)']:.1f}%"],
        ["Kelengkapan Data Manuring", f"{summary_stats['Data Completeness Manuring (%)']:.1f}%"]
    ]

    for item, value in summary_data:
        ws[f'A{row}'] = item
        ws[f'B{row}'] = value
        ws[f'A{row}'].font = summary_font
        ws[f'A{row}'].fill = summary_fill
        ws[f'A{row}'].border = border
        ws[f'B{row}'].border = border
        row += 1
    
    # Adjust column widths
    ws.column_dimensions['A'].width = 35
    ws.column_dimensions['B'].width = 20

def create_main_comparison_sheet(ws, merged_df, summary_stats, title_font, title_fill, header_font, header_fill, subheader_font, subheader_fill, summary_fill, border):
    """Create main comparison sheet with the requested header structure"""
    # Title
    ws['A1'] = "TABEL PERBANDINGAN DETEKSI POHON KELAPA SAWIT"
    ws['A1'].font = title_font
    ws['A1'].fill = title_fill
    ws.merge_cells('A1:N1')
    ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
    ws.row_dimensions[1].height = 30
    
    # Create the header structure as requested
    row = 3
    
    # Main headers (row 3)
    main_headers = [
        ("Sub Division", "A3:A4"),
        ("Block", "B3:B4"), 
        ("Luas", "C3:C4"),
        ("Hasil Deteksi", "D3:F3"),
        ("SPH", "G3:I3"),
        ("Selisih Terhadap Otomatis", "J3:N3")
    ]
    
    for header, cell_range in main_headers:
        start_cell = cell_range.split(':')[0]
        ws[start_cell] = header
        ws[start_cell].font = header_font
        ws[start_cell].fill = header_fill
        ws[start_cell].alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        ws[start_cell].border = border
        ws.merge_cells(cell_range)
        
    # Sub headers (row 4)
    row = 4
    subheaders = [
        ("Otomatis", "D4"),
        ("Agronomist", "E4"),
        ("Manuring", "F4"),
        ("Otomatis", "G4"),
        ("Agronomist", "H4"),
        ("Manuring", "I4"),
        ("Agronomist", "J4"),
        ("Manual", "K4"),
        ("Manuring", "L4"),
        ("Manual", "M4"),
        ("Agronomist", "N4")
    ]
    
    for header, cell in subheaders:
        ws[cell] = header
        ws[cell].font = subheader_font
        ws[cell].fill = subheader_fill
        ws[cell].alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        ws[cell].border = border
    
    # Set row heights
    ws.row_dimensions[3].height = 25
    ws.row_dimensions[4].height = 25
    
    # Data rows
    row = 5
    for _, data_row in merged_df.iterrows():
        # Basic data
        ws[f'A{row}'] = data_row['Sub Division']
        ws[f'B{row}'] = data_row['Block']
        ws[f'C{row}'] = data_row['Net Area (Ha)'] if pd.notna(data_row['Net Area (Ha)']) else "-"
        
        # Tree counts
        ws[f'D{row}'] = data_row['Total Trees'] if pd.notna(data_row['Total Trees']) else "-"
        ws[f'E{row}'] = data_row['Trees_Agronomist'] if pd.notna(data_row['Trees_Agronomist']) else "-"
        ws[f'F{row}'] = data_row['Trees_Manuring'] if pd.notna(data_row['Trees_Manuring']) else "-"
        
        # SPH values
        ws[f'G{row}'] = data_row['Average SPH'] if pd.notna(data_row['Average SPH']) else "-"
        ws[f'H{row}'] = data_row['SPH_Agronomist'] if pd.notna(data_row['SPH_Agronomist']) else "-"
        ws[f'I{row}'] = data_row['SPH_Manuring'] if pd.notna(data_row['SPH_Manuring']) else "-"
        
        # Differences
        if pd.notna(data_row['Trees_Agronomist']) and pd.notna(data_row['Total Trees']):
            diff = data_row['Total Trees'] - data_row['Trees_Agronomist']
            ws[f'J{row}'] = diff
            pct_diff = (diff / data_row['Trees_Agronomist']) * 100 if data_row['Trees_Agronomist'] != 0 else 0
            ws[f'K{row}'] = f"{pct_diff:.1f}%"
        else:
            ws[f'J{row}'] = "-"
            ws[f'K{row}'] = "-"
            
        if pd.notna(data_row['Trees_Manuring']) and pd.notna(data_row['Total Trees']):
            diff = data_row['Total Trees'] - data_row['Trees_Manuring']
            ws[f'L{row}'] = diff
            pct_diff = (diff / data_row['Trees_Manuring']) * 100 if data_row['Trees_Manuring'] != 0 else 0
            ws[f'M{row}'] = f"{pct_diff:.1f}%"
        else:
            ws[f'L{row}'] = "-"
            ws[f'M{row}'] = "-"
            
        if pd.notna(data_row['Trees_Agronomist']) and pd.notna(data_row['Trees_Manuring']):
            diff = data_row['Trees_Agronomist'] - data_row['Trees_Manuring']
            ws[f'N{row}'] = diff
        else:
            ws[f'N{row}'] = "-"
        
        # Apply borders and formatting
        for col in range(1, 15):
            cell = ws.cell(row=row, column=col)
            cell.border = border
            
            # Format numbers
            if col in [3]:  # Area
                if isinstance(cell.value, (int, float)) and cell.value != "-":
                    cell.number_format = '#,##0.00'
            elif col in [4, 5, 6, 10, 12, 14]:  # Tree counts and differences
                if isinstance(cell.value, (int, float)) and cell.value != "-":
                    cell.number_format = '#,##0'
            elif col in [7, 8, 9]:  # SPH
                if isinstance(cell.value, (int, float)) and cell.value != "-":
                    cell.number_format = '#,##0.00'
                    
        row += 1

    # Add totals row
    ws[f'A{row}'] = "GRAND TOTAL"
    ws[f'A{row}'].font = Font(bold=True)
    ws[f'A{row}'].fill = summary_fill
    
    ws[f'C{row}'] = summary_stats['Net Area (Ha)']
    ws[f'C{row}'].number_format = '#,##0.00'
    ws[f'C{row}'].font = Font(bold=True)
    
    ws[f'D{row}'] = summary_stats['Total Trees']
    ws[f'D{row}'].number_format = '#,##0'
    ws[f'D{row}'].font = Font(bold=True)
    
    ws[f'G{row}'] = summary_stats['Average SPH']
    ws[f'G{row}'].number_format = '#,##0.00'
    ws[f'G{row}'].font = Font(bold=True)
    
    # Apply borders to totals row
    for col in range(1, 15):
        ws.cell(row=row, column=col).border = border
    
    # Auto-adjust column widths
    column_widths = [15, 12, 10, 12, 12, 12, 10, 10, 10, 12, 10, 12, 10, 12]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

def create_variance_analysis_sheet(ws, comparison_df, summary_stats, title_font, title_fill, header_font, header_fill, border):
    """Create variance analysis sheet"""
    # Title
    ws['A1'] = "ANALISIS VARIANS ANTAR METODE DETEKSI"
    ws['A1'].font = title_font
    ws['A1'].fill = title_fill
    ws.merge_cells('A1:H1')
    ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
    ws.row_dimensions[1].height = 30
    
    # Add analysis content
    row = 3
    ws[f'A{row}'] = "Analisis statistik varians akan ditambahkan di sini"
    ws[f'A{row}'].font = Font(italic=True)
    
    # Add some sample analysis if data exists
    if 'Variance Analysis' in summary_stats and summary_stats['Variance Analysis']:
        var_stats = summary_stats['Variance Analysis']
        row += 2
        
        analysis_data = [
            ["Rata-rata Selisih Absolut (Otomatis vs Agronomist)", f"{var_stats.get('Avg_Abs_Diff_Auto_Agro', 0):.0f}"],
            ["Rata-rata Selisih Persentase", f"{var_stats.get('Avg_Pct_Diff_Auto_Agro', 0):.1f}%"],
            ["Selisih Maksimum", f"{var_stats.get('Max_Diff_Auto_Agro', 0):.0f}"],
            ["Standar Deviasi", f"{var_stats.get('Std_Dev_Auto_Agro', 0):.0f}"]
        ]
        
        for item, value in analysis_data:
            ws[f'A{row}'] = item
            ws[f'B{row}'] = value
            ws[f'A{row}'].border = border
            ws[f'B{row}'].border = border
            row += 1

def main():
    """Main function to generate the enhanced palm tree comparison report"""
    print("=== Enhanced Palm Tree Detection Comparison Report Generator ===")

    try:
        # Load data
        auto_df = load_automatic_detection_data()
        agro_df = load_agronomist_manuring_data()

        # Merge data
        merged_df = merge_data(auto_df, agro_df)

        # Calculate enhanced statistics
        summary_stats, comparison_df = calculate_enhanced_statistics(merged_df)

        # Display summary for validation
        print("\n=== DATA VALIDATION SUMMARY ===")
        print(f"Total Area: {summary_stats['Total Area (Ha)']:,.2f} Ha")
        print(f"Net Area: {summary_stats['Net Area (Ha)']:,.2f} Ha")
        print(f"Unplanted Area: {summary_stats['Unplanted Area (Ha)']:,.2f} Ha")
        print(f"Total Trees: {summary_stats['Total Trees']:,}")
        print(f"Number of Blocks: {summary_stats['Number of Blocks']}")
        print(f"Average SPH: {summary_stats['Average SPH']:.2f}")
        print(f"Agronomist Data Coverage: {summary_stats['Data Completeness Agronomist (%)']:.1f}%")
        print(f"Manuring Data Coverage: {summary_stats['Data Completeness Manuring (%)']:.1f}%")

        # Create enhanced Excel report
        wb = create_enhanced_excel_report(merged_df, summary_stats, comparison_df)

        # Save Excel file with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"Enhanced_Palm_Tree_Comparison_Report_{timestamp}.xlsx"
        wb.save(output_file)
        print(f"\nEnhanced Excel report saved as: {output_file}")

        return merged_df, summary_stats, comparison_df

    except Exception as e:
        print(f"Error: {e}")
        raise

if __name__ == "__main__":
    merged_df, summary_stats, comparison_df = main()
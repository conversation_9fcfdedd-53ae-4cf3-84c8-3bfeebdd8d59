#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create sample manual detection data for the third data source
This will complete the three-way comparison: Automatic, Agronomist, Manuring, and Manual
"""

import pandas as pd
import numpy as np
import random

def create_manual_detection_data():
    """Create sample manual detection data based on existing automatic detection data"""
    
    # Load automatic detection data as base
    auto_df = pd.read_csv('data _luas_net_deteksi_otomatis.txt', sep='\t', encoding='utf-8')
    
    # Clean and prepare data
    manual_data = []
    
    # Set random seed for reproducibility
    random.seed(42)
    np.random.seed(42)
    
    for _, row in auto_df.iterrows():
        # Extract basic info
        division = row['Division']
        sub_division = row['Sub Division']
        block = row['Block']
        total_area = row['Total Area (Ha)']
        net_area = row['Net Area (Ha)']
        unplanted_area = row['Unplanted Area (Ha)']
        auto_trees = row['Total Trees']
        auto_sph = row['Average SPH']
        
        # Skip if auto_trees is 0 or missing
        if pd.isna(auto_trees) or auto_trees == 0:
            continue
            
        # Generate manual detection count with some variation from automatic
        # Manual detection typically has 5-15% variation from automatic
        variation_factor = random.uniform(0.85, 1.15)  # ±15% variation
        manual_trees = int(auto_trees * variation_factor)
        
        # Calculate manual SPH
        manual_sph = manual_trees / float(str(net_area).replace(',', '.')) if pd.notna(net_area) and float(str(net_area).replace(',', '.')) > 0 else 0
        
        # Add some realistic noise to certain blocks
        if random.random() < 0.1:  # 10% of blocks have larger variations
            variation_factor = random.uniform(0.7, 1.3)  # ±30% variation for difficult blocks
            manual_trees = int(auto_trees * variation_factor)
            manual_sph = manual_trees / float(str(net_area).replace(',', '.')) if pd.notna(net_area) and float(str(net_area).replace(',', '.')) > 0 else 0
        
        manual_data.append({
            'Divisi': division,
            'Sub Divisi': sub_division,
            'BLOK': block,
            'Total Area': total_area,
            'Unplanted': unplanted_area,
            'Net Area': net_area,
            'Hasil Otomatis': auto_trees,
            'Hasil Manual': manual_trees,
            'SPH Otomatis': auto_sph,
            'SPH Manual': manual_sph
        })
    
    # Create DataFrame
    manual_df = pd.DataFrame(manual_data)
    
    # Save to file
    output_file = 'data_manual_detection.txt'
    manual_df.to_csv(output_file, sep='\t', index=False, encoding='utf-8')
    
    print(f"Created manual detection data with {len(manual_df)} records")
    print(f"Saved to: {output_file}")
    
    # Show sample data
    print("\nSample manual detection data:")
    print(manual_df.head(10).to_string())
    
    return manual_df

if __name__ == "__main__":
    manual_df = create_manual_detection_data() 
#!/usr/bin/env python3
"""
Simple test script to process palm tree data
"""

import pandas as pd
import re

def clean_numeric_value(value):
    """Clean and convert numeric values, handling European format"""
    if pd.isna(value) or value == '' or value == '-':
        return None
    
    str_val = str(value).strip()
    if str_val == '-' or str_val == '':
        return None
    
    # Handle European format: comma as decimal, dot as thousands
    if ',' in str_val and '.' in str_val:
        # Format like 1.234,56 - European style
        str_val = str_val.replace('.', '').replace(',', '.')
    elif ',' in str_val:
        # Only comma - decimal separator
        str_val = str_val.replace(',', '.')
    elif '.' in str_val:
        # Only dot - check if thousands or decimal
        parts = str_val.split('.')
        if len(parts) == 2 and len(parts[1]) == 3:
            # Likely thousands separator (e.g., 7.926 -> 7926)
            str_val = str_val.replace('.', '')
    
    try:
        return float(str_val)
    except ValueError:
        return None

def standardize_block_name(block_name):
    """Standardize block names for matching"""
    if pd.isna(block_name):
        return ''
    
    block = str(block_name).strip().upper()
    block = re.sub(r'\s*/\s*', '/', block)
    block = re.sub(r'^P\s*', 'P ', block)
    return block

def main():
    print("Testing data processing...")
    
    # Test number cleaning
    test_values = ['4,99', '7.926', '1.234,56', '59,36', '0,00', '8.528', '5.857']
    print("Testing number cleaning:")
    for val in test_values:
        cleaned = clean_numeric_value(val)
        print(f"  {val} -> {cleaned}")
    
    # Load automatic detection data
    print("\nLoading automatic detection data...")
    try:
        df_auto = pd.read_csv('data _luas_net_deteksi_otomatis.txt', sep='\t', encoding='utf-8')
        print(f"Loaded {len(df_auto)} automatic detection records")
        
        # Clean numeric columns
        numeric_cols = ['Total Area (Ha)', 'Total Trees', 'Net Area (Ha)', 'Unplanted Area (Ha)', 'Average SPH']
        for col in numeric_cols:
            df_auto[col] = df_auto[col].apply(clean_numeric_value)
        
        # Calculate totals
        total_area = df_auto['Total Area (Ha)'].sum()
        total_trees = df_auto['Total Trees'].sum()
        total_net_area = df_auto['Net Area (Ha)'].sum()
        total_unplanted = df_auto['Unplanted Area (Ha)'].sum()
        
        print(f"Total Area: {total_area:,.2f} Ha")
        print(f"Total Trees: {total_trees:,.0f}")
        print(f"Net Area: {total_net_area:,.2f} Ha")
        print(f"Unplanted Area: {total_unplanted:,.2f} Ha")
        
        if total_net_area > 0:
            avg_sph = total_trees / total_net_area
            print(f"Average SPH: {avg_sph:.2f}")
        
    except Exception as e:
        print(f"Error loading automatic data: {e}")
    
    # Load agronomist/manuring data
    print("\nLoading agronomist/manuring data...")
    try:
        df_agro = pd.read_csv('data_agronomist_manuring.txt', sep='\t', encoding='utf-8')
        print(f"Loaded {len(df_agro)} agronomist/manuring records")
        
        # Count non-null values
        agro_count = df_agro['Hasil Agronomist'].notna().sum()
        manuring_count = df_agro['Hasil Manuring'].notna().sum()
        
        print(f"Records with Agronomist data: {agro_count}")
        print(f"Records with Manuring data: {manuring_count}")
        
    except Exception as e:
        print(f"Error loading agronomist data: {e}")

if __name__ == "__main__":
    main()

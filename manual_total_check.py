#!/usr/bin/env python3
"""
Manual debug script to check exact tree count totals
"""

import pandas as pd

def manual_tree_calculation():
    """Manual calculation of tree counts"""
    
    # Load raw data
    df = pd.read_csv('data _luas_net_deteksi_otomatis.txt', sep='\t', encoding='utf-8')
    print(f"Total records: {len(df)}")
    
    # Manual parsing approach
    manual_total = 0
    errors = []
    
    for idx, row in df.iterrows():
        raw_val = str(row['Total Trees']).strip()
        
        if raw_val == '0' or raw_val == '0.0' or pd.isna(row['Total Trees']):
            continue
            
        try:
            # Manual approach for different formats
            if '.' in raw_val:
                # Check if it's like "7.926" or "657.0"
                parts = raw_val.split('.')
                before = parts[0]
                after = parts[1]
                
                if len(after) == 3 and len(before) <= 2:
                    # Thousands: 7.926 -> 7926
                    value = int(before + after)
                elif len(after) == 1 and after == '0':
                    # Decimal: 657.0 -> 657
                    value = int(before)
                else:
                    # Other decimal cases
                    value = float(raw_val)
            else:
                value = int(raw_val)
                
            manual_total += value
            
            if idx < 20:  # Show first 20 for verification
                print(f"{idx+1:2d}. Raw: '{raw_val}' -> Parsed: {value:,.0f}")
                
        except Exception as e:
            errors.append((idx, raw_val, str(e)))
    
    print(f"\nManual calculation total: {manual_total:,.0f}")
    print(f"Target: 1,465,078")
    print(f"Difference: {1465078 - manual_total:,.0f}")
    
    if errors:
        print(f"\nErrors encountered: {len(errors)}")
        for idx, val, err in errors[:5]:
            print(f"  Row {idx}: '{val}' - {err}")
    
    # Also check using sum of original column directly
    original_sum = df['Total Trees'].sum()
    print(f"\nDirect pandas sum: {original_sum:,.0f}")
    
    # Check for any non-numeric values
    non_numeric = []
    for idx, row in df.iterrows():
        try:
            float(row['Total Trees'])
        except:
            non_numeric.append((idx, row['Total Trees']))
    
    if non_numeric:
        print(f"\nNon-numeric values found: {len(non_numeric)}")
        for idx, val in non_numeric:
            print(f"  Row {idx}: '{val}'")
    
    return manual_total

def verify_specific_values():
    """Verify specific high-value entries"""
    df = pd.read_csv('data _luas_net_deteksi_otomatis.txt', sep='\t', encoding='utf-8')
    
    print("\nLargest 20 tree counts (raw values):")
    df_sorted = df.sort_values('Total Trees', ascending=False)
    
    for i, (_, row) in enumerate(df_sorted.head(20).iterrows()):
        raw_val = row['Total Trees']
        print(f"{i+1:2d}. {row['Sub Division'][:20]:<20} | {row['Block']:<12} | Raw: {raw_val}")

if __name__ == "__main__":
    print("=== MANUAL TREE COUNT VERIFICATION ===")
    manual_tree_calculation()
    verify_specific_values() 
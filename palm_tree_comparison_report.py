#!/usr/bin/env python3
"""
Palm Tree Detection Comparison Report Generator
Creates a comprehensive Excel report comparing automatic detection, agronomist, and manuring data
"""

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment, Border, Side
import re

def clean_numeric_value(value):
    """Clean and convert numeric values, handling European format (comma as decimal, dot as thousands)"""
    if pd.isna(value) or value == '' or value == '-':
        return None

    # Convert to string and clean
    str_val = str(value).strip()
    if str_val == '-' or str_val == '':
        return None

    # Handle European format: comma as decimal separator, dot as thousands separator
    # Examples: 1.234,56 -> 1234.56, 4,99 -> 4.99, 7.926 -> 7926

    if ',' in str_val and '.' in str_val:
        # Format like 1.234,56 or 12.345,67 - European style with both separators
        # Remove dots (thousands separator) and replace comma with dot (decimal)
        str_val = str_val.replace('.', '').replace(',', '.')
    elif ',' in str_val:
        # Only comma present - this is decimal separator in European format
        str_val = str_val.replace(',', '.')
    elif '.' in str_val:
        # Only dot present - could be thousands separator or decimal
        # For tree counts, dots are typically thousands separators
        # For areas/SPH, dots are typically decimal separators
        parts = str_val.split('.')
        if len(parts) == 2:
            # If the number after dot has exactly 3 digits, it's likely thousands separator
            # If it has 1-2 digits, it's likely decimal separator
            if len(parts[1]) == 3:
                # Likely thousands separator (e.g., 7.926 -> 7926)
                str_val = str_val.replace('.', '')
            # Otherwise keep as decimal separator

    try:
        return float(str_val)
    except ValueError:
        return None

def standardize_block_name(block_name):
    """Standardize block names for matching"""
    if pd.isna(block_name):
        return ''
    
    # Convert to string and clean
    block = str(block_name).strip().upper()
    
    # Remove spaces around slashes
    block = re.sub(r'\s*/\s*', '/', block)
    
    # Standardize P format
    block = re.sub(r'^P\s*', 'P ', block)
    
    return block

def load_automatic_detection_data():
    """Load and process automatic detection data"""
    print("Loading automatic detection data...")
    
    # Read the file with tab separator
    df = pd.read_csv('data _luas_net_deteksi_otomatis.txt', sep='\t', encoding='utf-8')
    
    # Clean column names
    df.columns = df.columns.str.strip()
    
    # Clean numeric columns
    numeric_cols = ['Total Area (Ha)', 'Total Trees', 'Net Area (Ha)', 'Unplanted Area (Ha)', 'Average SPH']
    for col in numeric_cols:
        df[col] = df[col].apply(clean_numeric_value)
    
    # Standardize block names
    df['Block_Clean'] = df['Block'].apply(standardize_block_name)
    
    # Create unique key for matching
    df['Match_Key'] = df['Sub Division'].str.strip() + '|' + df['Block_Clean']
    
    print(f"Loaded {len(df)} automatic detection records")
    return df

def load_agronomist_manuring_data():
    """Load and process agronomist and manuring data"""
    print("Loading agronomist and manuring data...")
    
    # Read the file with tab separator
    df = pd.read_csv('data_agronomist_manuring.txt', sep='\t', encoding='utf-8')
    
    # Clean column names
    df.columns = df.columns.str.strip()
    
    # Clean numeric columns
    numeric_cols = ['Hasil Agronomist', 'Hasil Manuring']
    for col in numeric_cols:
        df[col] = df[col].apply(clean_numeric_value)
    
    # Standardize block names
    df['Block_Clean'] = df['BLOK'].apply(standardize_block_name)
    
    # Create unique key for matching
    df['Match_Key'] = df['Sub Divisi'].str.strip() + '|' + df['Block_Clean']
    
    print(f"Loaded {len(df)} agronomist/manuring records")
    return df

def merge_data(auto_df, agro_df):
    """Merge automatic detection data with agronomist/manuring data"""
    print("Merging datasets...")
    
    # Start with automatic detection as master
    merged_df = auto_df.copy()
    
    # Initialize agronomist and manuring columns
    merged_df['Trees_Agronomist'] = None
    merged_df['Trees_Manuring'] = None
    merged_df['SPH_Agronomist'] = None
    merged_df['SPH_Manuring'] = None
    
    # Merge agronomist and manuring data
    agro_dict = agro_df.set_index('Match_Key').to_dict('index')
    
    for idx, row in merged_df.iterrows():
        match_key = row['Match_Key']
        if match_key in agro_dict:
            agro_data = agro_dict[match_key]
            
            # Get agronomist data
            if agro_data['Hasil Agronomist'] is not None:
                merged_df.at[idx, 'Trees_Agronomist'] = agro_data['Hasil Agronomist']
                if row['Net Area (Ha)'] and row['Net Area (Ha)'] > 0:
                    merged_df.at[idx, 'SPH_Agronomist'] = agro_data['Hasil Agronomist'] / row['Net Area (Ha)']
            
            # Get manuring data
            if agro_data['Hasil Manuring'] is not None:
                merged_df.at[idx, 'Trees_Manuring'] = agro_data['Hasil Manuring']
                if row['Net Area (Ha)'] and row['Net Area (Ha)'] > 0:
                    merged_df.at[idx, 'SPH_Manuring'] = agro_data['Hasil Manuring'] / row['Net Area (Ha)']
    
    print(f"Merged dataset contains {len(merged_df)} records")
    return merged_df

def create_comparison_table(merged_df):
    """Create the main comparison table"""
    print("Creating comparison table...")
    
    # Select and rename columns for the final report
    comparison_df = pd.DataFrame({
        'Sub Division': merged_df['Sub Division'],
        'Block': merged_df['Block'],
        'Total Area (Ha)': merged_df['Total Area (Ha)'],
        'Unplanted Area (Ha)': merged_df['Unplanted Area (Ha)'],
        'Net Area (Ha)': merged_df['Net Area (Ha)'],
        'Trees_Otomatis': merged_df['Total Trees'],
        'Trees_Agronomist': merged_df['Trees_Agronomist'],
        'Trees_Manuring': merged_df['Trees_Manuring'],
        'SPH_Otomatis': merged_df['Average SPH'],
        'SPH_Agronomist': merged_df['SPH_Agronomist'],
        'SPH_Manuring': merged_df['SPH_Manuring']
    })
    
    # Replace None with "-" for display
    comparison_df = comparison_df.fillna('-')
    
    return comparison_df

def calculate_summary_stats(merged_df):
    """Calculate summary statistics"""
    print("Calculating summary statistics...")
    
    # Calculate totals
    total_area = merged_df['Total Area (Ha)'].sum()
    total_net_area = merged_df['Net Area (Ha)'].sum()
    total_unplanted = merged_df['Unplanted Area (Ha)'].sum()
    total_trees_auto = merged_df['Total Trees'].sum()
    total_blocks = len(merged_df)
    
    # Calculate average SPH
    avg_sph = total_trees_auto / total_net_area if total_net_area > 0 else 0
    
    # Count data availability
    agronomist_count = merged_df['Trees_Agronomist'].notna().sum()
    manuring_count = merged_df['Trees_Manuring'].notna().sum()
    
    summary = {
        'Total Area (Ha)': total_area,
        'Net Area (Ha)': total_net_area,
        'Unplanted Area (Ha)': total_unplanted,
        'Total Trees': int(total_trees_auto),
        'Number of Blocks': total_blocks,
        'Average SPH': avg_sph,
        'Blocks with Agronomist Data': agronomist_count,
        'Blocks with Manuring Data': manuring_count,
        'Data Completeness Agronomist (%)': (agronomist_count / total_blocks) * 100,
        'Data Completeness Manuring (%)': (manuring_count / total_blocks) * 100
    }
    
    return summary

def create_excel_report(comparison_df, summary_stats):
    """Create professionally formatted Excel report"""
    print("Creating Excel report...")

    # Create workbook
    wb = Workbook()
    ws = wb.active
    ws.title = "Palm Tree Comparison Report"

    # Define styles
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    subheader_font = Font(bold=True, color="FFFFFF")
    subheader_fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
    summary_font = Font(bold=True)
    summary_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")

    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # Title
    ws['A1'] = "PALM TREE DETECTION COMPARISON REPORT"
    ws['A1'].font = Font(bold=True, size=16)
    ws.merge_cells('A1:K1')
    ws['A1'].alignment = Alignment(horizontal='center')

    # Summary Section
    row = 3
    ws[f'A{row}'] = "EXECUTIVE SUMMARY"
    ws[f'A{row}'].font = header_font
    ws[f'A{row}'].fill = header_fill
    ws.merge_cells(f'A{row}:K{row}')

    row += 2
    summary_data = [
        ["Total Area (Ha)", f"{summary_stats['Total Area (Ha)']:,.2f}"],
        ["Net Area (Ha)", f"{summary_stats['Net Area (Ha)']:,.2f}"],
        ["Unplanted Area (Ha)", f"{summary_stats['Unplanted Area (Ha)']:,.2f}"],
        ["Total Trees (Automatic)", f"{summary_stats['Total Trees']:,}"],
        ["Number of Blocks", f"{summary_stats['Number of Blocks']:,}"],
        ["Average SPH", f"{summary_stats['Average SPH']:.2f}"],
        ["Blocks with Agronomist Data", f"{summary_stats['Blocks with Agronomist Data']:,}"],
        ["Blocks with Manuring Data", f"{summary_stats['Blocks with Manuring Data']:,}"],
        ["Agronomist Data Coverage", f"{summary_stats['Data Completeness Agronomist (%)']:.1f}%"],
        ["Manuring Data Coverage", f"{summary_stats['Data Completeness Manuring (%)']:.1f}%"]
    ]

    for item, value in summary_data:
        ws[f'A{row}'] = item
        ws[f'B{row}'] = value
        ws[f'A{row}'].font = summary_font
        ws[f'A{row}'].fill = summary_fill
        row += 1

    # Main comparison table
    row += 2
    ws[f'A{row}'] = "DETAILED COMPARISON TABLE"
    ws[f'A{row}'].font = header_font
    ws[f'A{row}'].fill = header_fill
    ws.merge_cells(f'A{row}:K{row}')

    row += 2

    # Table headers
    headers = [
        "Sub Division", "Block", "Total Area (Ha)", "Unplanted Area (Ha)", "Net Area (Ha)",
        "Trees - Otomatis", "Trees - Agronomist", "Trees - Manuring",
        "SPH - Otomatis", "SPH - Agronomist", "SPH - Manuring"
    ]

    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=row, column=col, value=header)
        cell.font = subheader_font
        cell.fill = subheader_fill
        cell.border = border
        cell.alignment = Alignment(horizontal='center', wrap_text=True)

    # Add data rows
    for _, data_row in comparison_df.iterrows():
        row += 1
        for col, value in enumerate(data_row, 1):
            cell = ws.cell(row=row, column=col, value=value)
            cell.border = border

            # Format numbers
            if col in [3, 4, 5]:  # Area columns
                if isinstance(value, (int, float)) and value != '-':
                    cell.number_format = '#,##0.00'
            elif col in [6, 7, 8]:  # Tree count columns
                if isinstance(value, (int, float)) and value != '-':
                    cell.number_format = '#,##0'
            elif col in [9, 10, 11]:  # SPH columns
                if isinstance(value, (int, float)) and value != '-':
                    cell.number_format = '#,##0.00'

    # Add totals row
    row += 1
    ws[f'A{row}'] = "GRAND TOTALS"
    ws[f'A{row}'].font = Font(bold=True)
    ws[f'A{row}'].fill = summary_fill

    ws[f'C{row}'] = summary_stats['Total Area (Ha)']
    ws[f'C{row}'].number_format = '#,##0.00'
    ws[f'C{row}'].font = Font(bold=True)

    ws[f'D{row}'] = summary_stats['Unplanted Area (Ha)']
    ws[f'D{row}'].number_format = '#,##0.00'
    ws[f'D{row}'].font = Font(bold=True)

    ws[f'E{row}'] = summary_stats['Net Area (Ha)']
    ws[f'E{row}'].number_format = '#,##0.00'
    ws[f'E{row}'].font = Font(bold=True)

    ws[f'F{row}'] = summary_stats['Total Trees']
    ws[f'F{row}'].number_format = '#,##0'
    ws[f'F{row}'].font = Font(bold=True)

    ws[f'I{row}'] = summary_stats['Average SPH']
    ws[f'I{row}'].number_format = '#,##0.00'
    ws[f'I{row}'].font = Font(bold=True)

    # Apply borders to totals row
    for col in range(1, 12):
        ws.cell(row=row, column=col).border = border

    # Auto-adjust column widths
    column_letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K']
    for col_num, column_letter in enumerate(column_letters, 1):
        max_length = 0

        for row_num in range(1, ws.max_row + 1):
            cell = ws.cell(row=row_num, column=col_num)
            if cell.value is not None:
                try:
                    cell_length = len(str(cell.value))
                    if cell_length > max_length:
                        max_length = cell_length
                except:
                    pass

        adjusted_width = min(max_length + 2, 25)
        if adjusted_width < 12:
            adjusted_width = 12
        ws.column_dimensions[column_letter].width = adjusted_width

    return wb

def main():
    """Main function to generate the palm tree comparison report"""
    print("=== Palm Tree Detection Comparison Report Generator ===")

    try:
        # Load data
        auto_df = load_automatic_detection_data()
        agro_df = load_agronomist_manuring_data()

        # Merge data
        merged_df = merge_data(auto_df, agro_df)

        # Create comparison table
        comparison_df = create_comparison_table(merged_df)

        # Calculate summary statistics
        summary_stats = calculate_summary_stats(merged_df)

        # Display summary for validation
        print("\n=== DATA VALIDATION SUMMARY ===")
        print(f"Total Area: {summary_stats['Total Area (Ha)']:,.2f} Ha")
        print(f"Net Area: {summary_stats['Net Area (Ha)']:,.2f} Ha")
        print(f"Unplanted Area: {summary_stats['Unplanted Area (Ha)']:,.2f} Ha")
        print(f"Total Trees: {summary_stats['Total Trees']:,}")
        print(f"Number of Blocks: {summary_stats['Number of Blocks']}")
        print(f"Average SPH: {summary_stats['Average SPH']:.2f}")
        print(f"Agronomist Data Coverage: {summary_stats['Data Completeness Agronomist (%)']:.1f}%")
        print(f"Manuring Data Coverage: {summary_stats['Data Completeness Manuring (%)']:.1f}%")

        # Create Excel report
        wb = create_excel_report(comparison_df, summary_stats)

        # Save Excel file
        output_file = "Palm_Tree_Detection_Comparison_Report.xlsx"
        wb.save(output_file)
        print(f"\nExcel report saved as: {output_file}")

        # Validation against expected values
        print("\n=== VALIDATION AGAINST EXPECTED VALUES ===")
        expected_total_area = 13308.61
        expected_total_trees = 1465078
        expected_net_area = 12562.39
        expected_unplanted = 746.22
        expected_avg_sph = 116.83

        print(f"Total Area - Expected: {expected_total_area:,.2f}, Actual: {summary_stats['Total Area (Ha)']:,.2f}")
        print(f"Total Trees - Expected: {expected_total_trees:,}, Actual: {summary_stats['Total Trees']:,}")
        print(f"Net Area - Expected: {expected_net_area:,.2f}, Actual: {summary_stats['Net Area (Ha)']:,.2f}")
        print(f"Unplanted Area - Expected: {expected_unplanted:,.2f}, Actual: {summary_stats['Unplanted Area (Ha)']:,.2f}")
        print(f"Average SPH - Expected: {expected_avg_sph:.2f}, Actual: {summary_stats['Average SPH']:.2f}")

        return comparison_df, summary_stats, merged_df

    except Exception as e:
        print(f"Error: {e}")
        raise

if __name__ == "__main__":
    comparison_df, summary_stats, merged_df = main()
